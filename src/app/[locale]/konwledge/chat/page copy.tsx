'use client';
// 导入必要的依赖
import { useState, CSSProperties } from 'react';
import { message, Modal, Empty } from 'antd';
import { MenuOutlined, MessageOutlined, DeleteOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';

import useStore from '@/store/useStore';
import styles from './page.module.less';
import ChatComponent from './components/ChatComponent';
export const outerStyle: CSSProperties = {
  margin: 0,
  padding: 0,
  height: 'calc(100vh - 70px)',
};
export type StateIprops = {
  isSidebarOpen: boolean;
  curConvId: number | null;
};
const Chat: React.FC = () => {
  const { userInfo } = useStore();

  const [state, setState] = useState<StateIprops>({
    isSidebarOpen: true,
    curConvId: null,
  });
  const [convs, setConvs] = useState([
    {
      title: 'Conversation 1',
    },
    {
      title: 'Conversation 1',
    },
    {
      title: 'Conversation 1',
    },
    {
      title: 'Conversation 1',
    },
  ]);
  const goToConversation = (index) => {
    setState((prev) => ({ ...prev, curConvId: index }));
  };
  const deleteConversation = async (index: number) => {
    // 假设每个对话都有一个唯一 id
    try {
      // await del(`/api/conversation/${convToDelete.id}`); // 调用服务端删除接口

      const newConvs = [...convs];
      newConvs.splice(index, 1);
      setConvs(newConvs);

      if (state.curConvId === index) {
        setState((prev) => ({ ...prev, curConvId: null }));
      }

      message.success('删除成功');
    } catch (error) {
      message.error('删除失败，请重试');
    }
  };
  return (
    <Layout curActive="/konwledge/chat" outerStyle={outerStyle}>
      <div className={styles['chat-container']}>
        <div className={`${styles['conversations']} ${state.isSidebarOpen ? styles['is-open'] : ''}`}>
          <div className={styles['actions']}>
            <span style={{ fontWeight: 'bold', userSelect: 'none' }}>对话历史</span>
            <div
              className={styles['close']}
              onClick={() => setState({ ...state, isSidebarOpen: !state.isSidebarOpen })}
            >
              <MenuOutlined />
            </div>
          </div>
          <div className={styles['conversation-list']}>
            {convs.length === 0 ? (
              <Empty description="No conversations" />
            ) : (
              convs.map((conv, index) => (
                <div
                  key={index}
                  onClick={() => {
                    goToConversation(index);
                  }}
                  className={`${styles['conversation']} ${state.curConvId == index ? styles['active'] : ''}`}
                >
                  <div className={styles['conversation__title']}>
                    <MessageOutlined /> &nbsp; {conv.title}
                  </div>
                  <div className={styles['conversation__delete']}>
                    <DeleteOutlined
                      onClick={(event) => {
                        event.stopPropagation();
                        deleteConversation(index);
                      }}
                    />
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <ChatComponent state={state} setState={setState} />
      </div>
    </Layout>
  );
};

export default Chat;
