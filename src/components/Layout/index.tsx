'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Layout, Menu, theme, Avatar, Dropdown, ConfigProvider, type MenuProps, Spin, message } from 'antd';
import {
  DesktopOutlined,
  FileTextOutlined,
  Line<PERSON><PERSON>Outlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { AiOutlineDeploymentUnit } from 'react-icons/ai';
import { GrDocumentConfig } from 'react-icons/gr';
import {
  IoDocumentTextOutline,
  IoFileTrayOutline,
  IoMicCircleOutline,
  IoHeartCircleOutline,
  IoIdCardOutline,
  IoJournalOutline,
} from 'react-icons/io5';
import { HiOutlineClipboardDocumentList } from 'react-icons/hi2';
import { TbTemplate } from 'react-icons/tb';
import { LiaBuromobelexperte } from 'react-icons/lia';
import { RiUserCommunityLine } from 'react-icons/ri';
import { LuServerCog } from 'react-icons/lu';
import { CgMenuGridO } from 'react-icons/cg';
import { BsRobot } from 'react-icons/bs';

import { TbBoxModel } from 'react-icons/tb';
import { getThemeBg } from '@/utils';
import { usePathname, useParams } from 'next/navigation';
import { useRouter } from '@bprogress/next/app';
import { logout } from '@/services/user/api';
import useStore from '@/store/useStore';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import styles from './index.module.less';

// 设置 dayjs 语言为中文
dayjs.locale('zh-cn');

const { Header, Content, Sider } = Layout;

// 常量定义
const THEME_STORAGE_KEY = 'isDarkTheme';
const AGENT_PATH = '/agent';

// 图标映射
const iconMap: { [key: string]: React.ReactNode } = {
  DesktopOutlined: <DesktopOutlined />,
  AiOutlineDeploymentUnit: <AiOutlineDeploymentUnit />,
  GrDocumentConfig: <GrDocumentConfig />,
  IoDocumentTextOutline: <IoDocumentTextOutline />,
  HiOutlineClipboardDocumentList: <HiOutlineClipboardDocumentList />,
  TbTemplate: <TbTemplate />,
  LiaBuromobelexperte: <LiaBuromobelexperte />,
  RiUserCommunityLine: <RiUserCommunityLine />,
  LuServerCog: <LuServerCog />,
  CgMenuGridO: <CgMenuGridO />,
  IoFileTrayOutline: <IoFileTrayOutline />,
  IoMicCircleOutline: <IoMicCircleOutline />,
  IoHeartCircleOutline: <IoHeartCircleOutline />,
  IoIdCardOutline: <IoIdCardOutline />,
  IoJournalOutline: <IoJournalOutline />,
  FileTextOutlined: <FileTextOutlined />,
  LineChartOutlined: <LineChartOutlined />,
  TbBoxModel: <TbBoxModel />,
  ToolOutlined: <ToolOutlined />,
};

// 类型定义
interface MenuItem {
  path: string;
  title: string;
  icon: string;
  children?: MenuItem[];
  [propKey: string]: any;
}

interface SideMenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: SideMenuItem[];
}

interface IProps {
  children: React.ReactNode;
  curActive: string;
  defaultOpen?: string[];
  outerStyle?: React.CSSProperties;
  agentDetailData?: any;
  agentDetailDom?: (collapsed: boolean) => React.ReactNode;
}

// 退出登录函数
const handleLogout = async () => {
  try {
    localStorage.removeItem(THEME_STORAGE_KEY);
    const res = await logout();
    if (res.success) {
      globalThis.location.href = '/login';
    } else {
      message.error('退出登录失败');
    }
  } catch (error) {
    message.error('退出登录失败');
  }
};

// 下拉菜单配置
const dropdownItems: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="#">
        个人中心
      </a>
    ),
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="#">
        切换账户
      </a>
    ),
  },
  {
    key: '3',
    label: <span onClick={handleLogout}>退出登录</span>,
  },
];

const OptimizedLayout: React.FC<IProps> = ({
  children,
  curActive,
  outerStyle,
  defaultOpen = ['/'],
  agentDetailDom,
  agentDetailData,
}) => {
  const { token } = theme.useToken();

  // 状态管理
  const [collapsed, setCollapsed] = useState(false);
  const [navList, setNavList] = useState<SideMenuItem[]>([]);
  const [curTheme, setCurTheme] = useState<boolean>(false);
  const [sideMenu, setSideMenu] = useState<SideMenuItem[]>([]);
  const [isInitializing, setIsInitializing] = useState(true);

  // Hooks
  const router = useRouter();
  const pathname = usePathname();
  const appId = useParams().appId;
  const { userInfo, menuList, getMenuList, getUserInfo } = useStore();
  const { source, applyType } = agentDetailData ?? {};

  /**
   * 特殊处理智能体调配菜单的名称：applyType==chat,名称为“对话”
   * applyType==chat,名称为“对话”
   * applyType==workflow && source==dify，名称为“调试”
   * applyType==workflow && source==inter，名称为“编排”
   */
  const setMenuName = useCallback((): string => {
    if (applyType === 'chat') {
      return '对话';
    }
    if (applyType === 'workflow' && source === 'dify') {
      return '调试';
    }
    if (applyType === 'workflow' && source === 'inter') {
      return '编排';
    }
    // 默认返回空字符串，避免返回 undefined
    return '';
  }, [applyType, source]);
  //给menuList添加key和label属性
  // 使用 useCallback 优化函数性能
  const addMenuKeyAndLabel = useCallback(
    (menuList: MenuItem[]): SideMenuItem[] => {
      return menuList.map((item) => ({
        ...item,
        key: item.path.includes('${appId}') ? item.path.replace(/\$\{appId\}/g, appId as string) : item.path,
        label: item.title.includes('${title}') ? setMenuName() : item.title,
        icon: iconMap[item.icon],
        children: item.children ? addMenuKeyAndLabel(item.children) : undefined,
      }));
    },
    [appId, setMenuName],
  );

  const handleMenuClick = useCallback(
    (row: { key: string }) => {
      const updatedMenu = navList.find((item) => item.key === row.key)?.children || [];
      setSideMenu(updatedMenu);

      if (row.key === AGENT_PATH) {
        // 智能体特殊处理，它没有左侧二级菜单
        router.push(row.key);
      } else if (updatedMenu.length > 0) {
        router.push(updatedMenu[0].key);
      }
    },
    [navList, router],
  );

  const handleSelect = useCallback(
    (row: { key: string }) => {
      if (row.key.includes('http')) {
        window.open(row.key);
        return;
      }
      router.push(row.key);
    },
    [router],
  );

  const handleClick = useCallback(
    ({ key }: { key: string }) => {
      handleSelect({ key });
    },
    [handleSelect],
  );

  // 使用 useMemo 优化计算
  const processedNavList = useMemo(() => {
    if (menuList.length > 0) {
      return addMenuKeyAndLabel(menuList);
    }
    return [];
  }, [menuList, addMenuKeyAndLabel]);

  const currentSideMenu = useMemo(() => {
    const mainPath = '/' + pathname.split('/')[2];
    const matchedNav = processedNavList.find((item) => {
      return item.key === mainPath || pathname.startsWith(item.key);
    });
    return matchedNav?.children || [];
  }, [pathname, processedNavList]);

  const contentStyle = useMemo(
    () => ({
      height: 'calc(100vh - 79px)',
      overflowY: 'auto' as const,
      margin: '15px 16px 0',
      padding: 15,
      boxSizing: 'border-box' as const,
      ...getThemeBg(curTheme),
      borderRadius: token.borderRadiusSM,
      ...outerStyle,
    }),
    [curTheme, token.borderRadiusSM, outerStyle],
  );

  // 初始化主题
  useEffect(() => {
    const isDark = !!localStorage.getItem(THEME_STORAGE_KEY);
    setCurTheme(isDark);
  }, []);

  // 初始化用户信息和菜单
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsInitializing(true);

        const promises: Promise<void>[] = [];

        if (menuList.length === 0) {
          promises.push(getMenuList());
        }

        if (Object.keys(userInfo).length === 0) {
          promises.push(getUserInfo());
        }

        await Promise.all(promises);
      } catch (error) {
        message.error('初始化数据失败');
      } finally {
        setIsInitializing(false);
      }
    };

    initializeData();
  }, [menuList.length, userInfo, getMenuList, getUserInfo]);

  // 更新导航列表
  useEffect(() => {
    if (processedNavList.length > 0) {
      setNavList(processedNavList);
    }
  }, [processedNavList]);

  // 更新侧边菜单
  useEffect(() => {
    setSideMenu(currentSideMenu);
  }, [currentSideMenu]);

  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: curTheme ? theme.darkAlgorithm : theme.defaultAlgorithm,
        cssVar: true,
        hashed: false,
      }}
      locale={zhCN}
    >
      <Layout>
        <Header style={{ padding: 0, backgroundColor: '#001529', display: 'flex' }}>
          <div className={styles.warper}>
            <div className={styles.logo}>
              <div className={styles.icon}>
                <BsRobot />
              </div>
              <span>智能体矩阵</span>
            </div>

            <ul className={styles.nav}>
              {navList.map((item) => (
                <li
                  className={`${pathname.startsWith(item.key) ? styles.active : ''} ${styles.item}`}
                  key={item.key}
                  onClick={() => handleMenuClick({ key: item.key })}
                >
                  {item.label}
                </li>
              ))}
            </ul>

            <div className={styles.right}>
              <div className={styles.avatar}>
                <Dropdown menu={{ items: dropdownItems }} placement="bottomLeft" arrow>
                  <div>
                    <Avatar icon={<UserOutlined />} style={{ color: '#fff' }} size="large" />
                    <span style={{ color: '#fff' }}>{userInfo.userName || '用户'}</span>
                  </div>
                </Dropdown>
              </div>
            </div>
          </div>
        </Header>

        <Layout>
          {pathname !== AGENT_PATH && (
            <Sider
              trigger={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              theme="dark"
              breakpoint="lg"
              collapsedWidth="80"
              collapsible
              collapsed={collapsed}
              onCollapse={setCollapsed}
            >
              {agentDetailDom && agentDetailDom(collapsed)}
              <Menu
                theme="dark"
                mode="inline"
                defaultSelectedKeys={[curActive]}
                items={sideMenu}
                defaultOpenKeys={defaultOpen}
                onSelect={handleSelect}
                onClick={handleClick}
              />
            </Sider>
          )}

          <Content>
            <div style={contentStyle}>{children}</div>
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default OptimizedLayout;
