.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.chatMessages {
  padding: 16px;
}

.messageItem {
  display: flex;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.userMessage {
  flex-direction: row-reverse;
}

.assistantMessage {
  flex-direction: row;
}

.messageAvatar {
  flex-shrink: 0;
  margin: 0 12px;
}

.assistantAvatar {
  background-color: #e6f7ff;
  color: #1890ff;
}

.userAvatarWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.userAvatar {
  background-color: #e6f7ff;
  color: #1890ff;
}

.userName {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  text-align: center;
}

.messageContent {
  max-width: 70%;
}

.messageBubble {
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.userMessage .messageBubble {
  background-color: #69b1ff;
  border-top-right-radius: 2px;
  text-align: left;
}

.assistantMessage .messageBubble {
  background-color: #ffffff;
  border-top-left-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}