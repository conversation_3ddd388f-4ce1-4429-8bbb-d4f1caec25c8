'use client';
import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Upload, message, Collapse } from 'antd';
import { SendOutlined, UploadOutlined } from '@ant-design/icons';
import type { CollapseProps } from 'antd';
import Cookies from 'js-cookie';
import { tryRunningStream, detail } from '@/services/flow/api';
import ReactJsonView from '@/components/JsonView';
import { sortNodes } from './workUtil'

interface IProps {
  id: string;
  open: boolean;
  onClose: () => void;
  parameters: any;
}

const WorkResultDrawer: React.FC<IProps> = ({ id, open, onClose, parameters }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [executeResult, setExecuteResult] = useState<object>({});
  const [collapseItems, setCollapseItems] = useState<any[]>([])

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '开始节点',
      children: <ReactJsonView value={executeResult} minHeight={100} background={'#ffffff'} />,
      extra: <SendOutlined />,
    },
    {
      key: '2',
      label: '大模型',
      children: <ReactJsonView value={executeResult} minHeight={100} background={'#ffffff'} />,
      extra: <SendOutlined />,
    },
    {
      key: '3',
      label: '结束节点',
      children: <ReactJsonView value={executeResult} minHeight={100} background={'#ffffff'} />,
      extra: <SendOutlined />,
    },
  ];

  const onFinish = async (values: any) => {
    setSubmitLoading(true);
    const resp = await tryRunningStream({
      id: id,
      variables: values,
    });
    console.log('resp', resp)
    // if (resp.errorCode === 0) {
    //   message.success('运行成功');
    //   setExecuteResult(resp);
    // } else {
    //   message.error('运行失败');
    // }
    setSubmitLoading(false);
  };

  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  const onFinishFailed = (errorInfo: any) => {
    setSubmitLoading(false);
    message.error('失败：' + errorInfo);
    console.log('Failed:', errorInfo);
  };

  const getParameters = async () => {
    const resp = await detail(id);
    const nodeJson = JSON.parse(resp.data?.content);
    const nodes = sortNodes(nodeJson)
    setCollapseItems(nodes)
    console.log('resp', nodeJson)
  };

  useEffect(() => { getParameters() }, [id])

  return (
    <Drawer width={640} title="请输入参数" placement="right" closable={false} onClose={onClose} open={open}>
      <Form
        form={form}
        name="basic"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        {parameters &&
          parameters.map((item) => {
            let inputComponent;
            switch (item.dataType) {
              case 'File':
                inputComponent = (
                  <Upload
                    maxCount={1}
                    action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
                    headers={{ Authorization: Cookies.get('access_token') as string }}
                    onChange={({ file }) => {
                      if (file.status === 'done') {
                        let url = file.response?.response.url;
                        if (url.indexOf('http') < 0) {
                          url = window.location.origin + url;
                        }
                        form.setFieldsValue({
                          [item.name]: url,
                        });
                      }
                    }}
                  >
                    <Button icon={<UploadOutlined />}>上传</Button>
                  </Upload>
                );
                break;
              default:
                inputComponent = <Input />;
            }

            return (
              <Form.Item
                key={item.name}
                label={item.name}
                name={item.name}
                rules={[{ required: item.required }]}
                extra={item.description}
              >
                {inputComponent}
              </Form.Item>
            );
          })}

        <Form.Item wrapperCol={{ offset: 4, span: 18 }}>
          <Button disabled={submitLoading} loading={submitLoading} type="primary" htmlType="submit">
            <SendOutlined /> 开始运行
          </Button>
        </Form.Item>
      </Form>

      <div>
        <div>执行结果：</div>
        <div style={{ padding: 10, backgroundColor: '#F5F6F9' }}>
          <ReactJsonView value={executeResult} minHeight={100} />
        </div>
      </div>

      <div style={{ marginTop: 20 }}>
        <div>执行步骤：</div>
        <div>
          <Collapse
            items={collapseItems}
            defaultActiveKey={['1']}
            onChange={onChange}
            size="small"
          />
        </div>
      </div>
    </Drawer>
  );
};

export default WorkResultDrawer;
