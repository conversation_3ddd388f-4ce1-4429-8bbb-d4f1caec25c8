.cardWrapper {
    padding-bottom: 5px;
    margin-bottom: 5px;
    height: 195px;

    &:hover {
        .card {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.16);

            &::before {
                opacity: 1;
            }
        }
    }
}

.card {
    height: 190px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #2080ff, #6a5af9);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    // &:hover {
    //     transform: translateY(-5px);
    //     box-shadow: 0 8px 24px rgba(32, 128, 255, 0.15);

    //     &::before {
    //         opacity: 1;
    //     }
    // }

    :global {
        .ant-card-body {
            padding: 14px 10px 10px 14px !important;
            background: rgba(255, 255, 255, 0.9);
        }
    }

    .head {
        display: flex;
        flex-direction: row;
        gap: 12px;

        .name {
            flex: 1;

            .nameTitle {
                width: 90%;
                font-size: 16px;
                color: #151b26;
                display: -webkit-box;
                overflow: hidden;
                font-weight: bold;
                font-size: 14px;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: 500;
            }
        }

        .icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(32, 128, 255, 0.2);
            position: relative;
            overflow: hidden;

            &::after {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(to bottom right,
                        rgba(255, 255, 255, 0) 0%,
                        rgba(255, 255, 255, 0.1) 50%,
                        rgba(255, 255, 255, 0) 100%);
                transform: rotate(45deg);
            }
        }

        .animate__pulse {
            &::after {
                animation: shine 3s infinite;
            }
        }
    }

    .desc {
        display: -webkit-box;
        height: 60px;
        margin-top: 15px;
        font-size: 12px;
        color: #151b26;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        padding-left: 10px;

    }

    .footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 12px;
        color: #666;
        margin-top: 10px;
    }

    .others {
        position: absolute;
        right: 10px;
        top: 10px;
    }
}

@keyframes shine {
    0% {
        left: -100%;
        top: -100%;
    }

    100% {
        left: 100%;
        top: 100%;
    }
}