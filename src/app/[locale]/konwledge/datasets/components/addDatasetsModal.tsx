'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { create, update } from '@/services/konwledge/api';
import { UpdateParams } from '@/types/konwledge/index';

const { TextArea } = Input;

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<Template>;
  mode?: 'add' | 'edit';
}

interface Template {
  id?: number;
  name: string;
  content: string;
}

const AddDatasetsModal: React.FC<IProps> = ({ open, onCancel, onSuccess, initialValues, mode = 'add' }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      form.validateFields().then(async (values) => {
        setSubmitLoading(true);
        if (mode === 'add') {
          createHandle(values);
        } else {
          // 编辑模式
          updateHandle({ id: initialValues?.id, ...values });
        }
      });
    } catch (error) {
      message.error('保存失败');
    }
  };

  const createHandle = (data: Template) => {
    create(data).then((res) => {
      if (res.success) {
        message.success('创建成功');
        setSubmitLoading(false);
        onSuccess();
      } else {
        message.error(res.msg || '创建失败');
      }
    });
  };

  const updateHandle = (data: UpdateParams) => {
    update(data).then((res) => {
      if (res.success) {
        message.success('更新成功');
        setSubmitLoading(false);
        onSuccess();
      } else {
        message.error(res.msg || '更新失败');
      }
    });
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue(initialValues);
    }
    if (open && !initialValues) {
      form.resetFields();
    }
  }, [open, initialValues]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title={mode == 'add' ? '创建知识库' : '编辑知识库'}
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <Form.Item label="知识库名称" name="name" rules={[{ required: true, message: '请输入知识库名称' }]}>
          <Input placeholder="请输入知识库名称" />
        </Form.Item>

        <Form.Item label="描述" name="content">
          <TextArea placeholder="请输入知识库描述" rows={6} maxLength={1000} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddDatasetsModal;
