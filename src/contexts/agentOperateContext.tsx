import { createContext, useContext } from 'react';
import { AgentFormData } from '@/types/agent/index';

interface InjectMessageType {
  initValues: AgentFormData;
  getDetail: () => void;
  getApi: () => void;
}

// 创建上下文对象，使用更具描述性的名称
const AgentOperateContext = createContext<InjectMessageType | null>(null);

// 创建一个提供上下文值的组件
export const AgentOperateContextProvider = ({
  children,
  contextValue,
}: {
  children: React.ReactNode;
  contextValue: InjectMessageType;
}) => {
  return <AgentOperateContext.Provider value={contextValue}>{children}</AgentOperateContext.Provider>;
};

// 创建一个钩子来使用上下文
export const useAgentOperateContext = () => {
  const context = useContext(AgentOperateContext);
  if (!context) {
    throw new Error('useAgentListContext must be used within a AgentListContextProvider');
  }
  return context;
};
