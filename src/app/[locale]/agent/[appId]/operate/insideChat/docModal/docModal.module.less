.blue-border {
    border: 1px solid #1677FF;
}

.docList {
    height: 300px;
    overflow: scroll;

    :global {
        .ant-list-items .ant-list-item {
            margin-bottom: 5px;
            padding: 8px 5px;
            font-size: 18px;
            border: 1px solid #E0E0E0;
            border-radius: 5px;
            cursor: pointer;
            border-block-end: 1px solid #E0E0E0;

            &:hover {
                .blue-border();
            }

            &.selected {
                background-color: #e6f4ff;
                .blue-border()
            }

            &:last-child {
                border-block-end: 1px solid #E0E0E0;
            }

            &:last-child:hover,
            &:last-child.selected {
                border-block-end: 1px solid #1677FF;
            }

            .ant-list-item-meta-avatar {
                color: #1677FF;
            }

            .ant-list-item-meta {
                align-items: center;
            }
        }
    }
}

.selected {
    background-color: #e6f4ff;
    .blue-border()
}

.docIcon {
    color: #1677FF;
}