// 定义大模型列表查询数据
export interface LlmsListQuery {
  brand: string;
  title: string;
  pageNumber: number;
  pageSize: number;
}

// // 定义大语言模型配置信息
// export interface LLMModelConfig {
//   /** 品牌名称 */
//   brand: string;
//   /** 部门ID */
//   deptId: number;
//   /** 模型唯一标识ID */
//   id: string;
//   /** LLM API密钥 */
//   llmApiKey: string;
//   /** LLM端点地址 */
//   llmEndpoint: string;
//   /** LLM额外配置 */
//   llmExtraConfig: string;
//   /** LLM模型名称 */
//   llmModel: string;
//   /** 是否支持聊天功能 */
//   supportChat: boolean;
//   /** 是否支持嵌入功能 */
//   supportEmbed: boolean;
//   /** 支持的功能列表 */
//   supportFeatures: string[];
//   /** 是否支持函数调用 */
//   supportFunctionCalling: boolean;
//   /** 是否支持重排序 */
//   supportReranker: boolean;
//   /** 租户ID */
//   tenantId: number;
//   /** 模型标题/显示名称 */
//   title: string;
//   /** 查看链接地址 */
//   url?: string;
// }

export interface AddLLMModelData {
  brand: string;
  title: string;
  llmEndpoint: string;
  llmModel: string;
  llmApiKey: string;
  llmExtraConfig: string;
  description: string;
  supportChat: boolean;
  supportFunctionCalling: boolean;
  supportEmbed: boolean;
  supportReranker: boolean;
  supportTextToImage: boolean;
  supportImageToImage: boolean;
  supportTextToAudio: boolean;
  supportAudioToAudio: boolean;
  supportTextToVideo: boolean;
  supportImageToVideo: boolean;
  icon?: string;
}

export interface updateLLMModelData extends AddLLMModelData {
  id: string;
  /** 支持的功能列表 */
  supportFeatures?: string[];
}
