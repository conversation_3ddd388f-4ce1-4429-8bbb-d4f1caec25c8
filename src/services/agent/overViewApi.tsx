import req from '@/utils/request';

/**
 *   timeType * 时间范围类型
 * 1-今天
 * 2-过去7天
 * 3-过去2周
 * 4-本月至今
 * 5-过去1个月
 * 6-过去3个月
 * 7-过去12个月
 * 8-所有时间
 */
interface paramsType {
  id: string;
  timeType: string;
}

//全部消息数
export const allMsg = ({ id, timeType }: paramsType): Promise<SERES_RESPONSE> =>
  req.get(`/agent-log/v1/monitor/allMsg`, { params: { id, timeType } });

//数据吞吐量（响应体字符平均数）
export const avgResBody = ({ id, timeType }: paramsType): Promise<SERES_RESPONSE> =>
  req.get(`/agent-log/v1/monitor/avgResBody`, { params: { id, timeType } });

//平均响应时间
export const avgResTime = ({ id, timeType }: paramsType): Promise<SERES_RESPONSE> =>
  req.get(`/agent-log/v1/monitor/avgResTime`, { params: { id, timeType } });

//超时请求
export const timeoutReqCount = ({ id, timeType }: paramsType): Promise<SERES_RESPONSE> =>
  req.get(`/agent-log/v1/monitor/timeoutReqCount`, { params: { id, timeType } });

//错误数
export const errorReqCount = ({ id, timeType }: paramsType): Promise<SERES_RESPONSE> =>
  req.get(`/agent-log/v1/monitor/errorReqCount`, { params: { id, timeType } });
