'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, message, Upload, Select } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const { Option } = Select;

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const RelationModal: React.FC<IProps> = ({ open, onCancel, onSuccess }) => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);

  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);
      // TODO: 实现保存逻辑
      console.log(selected);
      message.success('关联成功');
      onSuccess();
    } catch (error: unknown) {
      if (error instanceof Error && 'errorFields' in error) {
        return;
      }
      message.error('关联失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  const changeHandle = (value: string[]) => {
    setSelected(value);
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open) {
      setSelected(['1']);
    }
  }, [open]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    setSelected([]);
    onCancel();
  };

  return (
    <Modal
      title="链接知识库"
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      <Select
        defaultValue={selected}
        style={{ width: 550 }}
        mode="multiple"
        placeholder="请选择权益履约方"
        suffixIcon={<SearchOutlined />}
        onChange={(value) => changeHandle(value)}
        filterOption={(input, option) => {
          if (!option || !option.label) return false;
          const label = String(option.label); // 确保 label 是字符串类型
          return label.includes(input);
        }}
        allowClear
      >
        <Option value="1" label="数据1">
          数据1
        </Option>
        <Option value="2" label="数据2">
          数据2
        </Option>
        <Option value="3" label="数据3">
          数据3
        </Option>
      </Select>
    </Modal>
  );
};

export default RelationModal;
