'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Table, Input, Button, Space, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { propmt, query } from '@/services/prompt/api';
import zhCN from 'antd/es/locale/zh_CN';
export interface Template {
  id: number;
  name: string;
  type: string;
  createTime: string;
  author: string;
  content: string;
}

interface TemplateSelectModalProps {
  open: boolean;
  onCancel: () => void;
  onSelect: (template: Template) => void;
}

interface QueryParams {
  params?: {
    name?: string;
  };
  current?: number;
  pageSize?: number;
}

const TemplateSelectModal: React.FC<TemplateSelectModalProps> = ({ open, onCancel, onSelect }) => {
  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Template[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pageSize, setPageSize] = useState<number>(10);
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  // 初始化加载
  useEffect(() => {
    fetchTemplates({ params: { name: searchName }, current, pageSize });
  }, [searchName, current, pageSize]);
  const fetchTemplates = async (params: QueryParams = {}) => {
    try {
      setLoading(true);
      const { resp, message, success } = await query(params).finally(() => {
        setLoading(false);
      });
      if (success && resp.length > 0) {
        setData(resp[0]?.list);
        setTotal(resp[0]?.pagination?.total);
      } else {
        message.error(message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    }
  };
  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  const columns: ColumnsType<Template> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '模板类型',
      dataIndex: 'type',
      width: 100,
      key: 'type',
    },
    {
      title: '模板内容',
      dataIndex: 'content',
      width: 500,
      ellipsis: true,
      key: 'content',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '创建人',
      dataIndex: 'author',
      key: 'author',
    },
  ];

  const handleSelect = () => {
    if (selectedRowKeys.length !== 1) {
      message.error('请选择一个模板');
      return;
    }
    const selectedTemplate = data.find((item) => item.id === selectedRowKeys[0]);
    if (selectedTemplate) {
      onSelect(selectedTemplate);
    }
  };

  return (
    <Modal
      title="选择模板"
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="select" type="primary" onClick={handleSelect}>
          确认
        </Button>,
      ]}
      width={1200}
    >
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="请输入模板名称"
          value={searchName}
          onChange={(e) => setSearchName(e.target.value)}
          style={{ width: 200 }}
        />
        <Button type="primary" onClick={() => fetchTemplates({ params: { name: searchName }, current, pageSize })}>
          查询
        </Button>
      </Space>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        rowSelection={{
          type: 'radio',
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
          defaultPageSize: 10,
          locale: zhCN.Pagination, // 设置分页的本地化为中文
          pageSize,
          onChange: PageChange,
          current,
          onShowSizeChange: PageChange,
          total,
        }}
      />
    </Modal>
  );
};

export default TemplateSelectModal;
