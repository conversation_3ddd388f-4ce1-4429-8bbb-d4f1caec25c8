/* stylelint-disable CssSyntaxError */
// .header-warper {
//     padding: 0;
//     background-color: '#001529';
//     // box-shadow: 0 4px 6px 0 rgba(8, 14, 26, 0.04), 0 1px 10px 0 rgba(8, 14, 26, 0.05), 0 2px 4px -1px rgba(8, 14, 26, 0.06);
// }

.warper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .logo {
        display: flex;
        // justify-content: center;
        align-items: center;
        margin-right: 15px;
        // width: 20%;
        // height: 45px;
        margin-left: 15px;
        // background-color: var(--ant-color-primary);
        // color: #000;
        border-radius: 6px;

        span {
            margin-left: 10px;
            color: #fff;
            font-size: 18px;
            letter-spacing: 1px;
        }

        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            color: #fff;
            font-size: 24px;
            line-height: 40px;
            background: var(--ant-color-primary);
            border-radius: 50%;
        }
    }

    .nav {
        display: flex;
        flex: 1;
        gap: 10px;
        padding-left: 100px;

        li.item {
            padding: 0 10px;
            color: #fff;
            font-size: 16px;
            list-style-type: none;
            cursor: pointer;
        }

        li.item:hover,
        li.active {
            color: #fff;
            background-color: var(--ant-color-primary);
        }

    }

    :global {
        .ant-menu-dark.ant-menu-horizontal>.ant-menu-item-selected {
            border: 1px solid var(--ant-color-primary);
            border-radius: var(--ant-menu-item-border-radius);
        }
    }

    .right {
        display: flex;
        justify-content: flex-end;
        width: 200px;
    }

    // 定义混合
    .mr(@gap: 26px) {
        margin-right: @gap;
    }

    // 使用混合时传递参数
    .avatar {
        cursor: pointer;
        .mr(26px); // 明确传递参数
    }

    .theme,
    .msg,
    .group,
    .i18n {
        cursor: pointer;
        .mr(26px); // 明确传递参数
    }
}