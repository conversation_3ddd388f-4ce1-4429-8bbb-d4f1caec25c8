.container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;

    @media (max-width: 1200px) {
        grid-template-columns: 1fr;
    }
}

.container-external {
    display: flex;
    justify-content: center;
    height: 100%;
}

.title {
    margin-top: 0;
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}