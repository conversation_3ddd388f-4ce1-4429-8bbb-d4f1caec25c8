/** @type {import('next').NextConfig} */
import withAntdLess from 'next-plugin-antd-less';
import createNextIntlPlugin from 'next-intl/plugin';
import dotenv from 'dotenv';

const ENV_TYPE = process.env.APP_ENV
if (!!ENV_TYPE) {
  dotenv.config({ path: `.env`, override: true, encoding: 'utf8' });
} else {
  dotenv.config({ path: `.env.${ENV_TYPE}`, override: true, encoding: 'utf8' });
}

const CORS_HEADERS = [
  {
    key: "Access-Control-Allow-Credentials",
    value: "true"
  },
  {
    key: "Access-Control-Allow-Origin",
    value: "*"
  },
  {
    key: "Access-Control-Allow-Methods",
    value: "GET,DELETE,PATCH,POST,PUT"
  },
  {
    key: "Access-Control-Allow-Headers",
    value: "Content-Type, Authorization",
  },
];

const nextConfig = {
  reactStrictMode: false,
  env: {
    "JWT_SECRET": "next-admin",
    "BASE_API_URL": "/api"
  },
  // distDir: 'build',//自定义输出目录
  // eslint: {
  //   // Warning: This allows production builds to successfully complete even if
  //   // your project has ESLint errors.
  //   ignoreDuringBuilds: true,
  // },

  //指定请求路径到页面目标的映射
  // exportPathMap: async function (
  //   defaultPathMap,
  //   { dev, dir, outDir, distDir, buildId }
  // ) {
  //   return {
  //     '/': { page: '/login' },
  //   }
  // },
  async headers() {
    // 跨域配置
    return [
      {
        source: "/favicon.ico",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400",
          },
        ],
      },
      {
        source: "/api/:path*", // 为访问 /api/** 的请求添加 CORS HTTP Headers
        headers: CORS_HEADERS
      },
      {
        source: "/specific", // 为特定路径的请求添加 CORS HTTP Headers,
        headers: CORS_HEADERS
      }
    ];
  },
  swcMinify: true,
  output: 'standalone',
  serverRuntimeConfig: {
    // port: 12000, // 设置服务器端口cd
    host: '0.0.0.0' // 设置服务器主机
  },

  async redirects() {
    return [
      {
        source: '/',
        destination: '/login',
        permanent: true
      }
    ];
  },
  images: {
    domains: ['mdn.alipayobjects.com'], // 允许所有域名的图片
    // // 如果你知道具体的域名，最好明确指定
    // // domains: ['your-domain.com', 'another-domain.com'],
    // unoptimized: false, // 启用图片优化
    unoptimized: true,
  },
  // experimental: {
  //   appDir: true,
  // },
  // ======== 编译优化 ========
  // compiler: {
  //   styledComponents: true, // 同时支持 styled-components
  // },

  // ======== Webpack 扩展配置 ========
  // webpack(config) {
  //   return config;
  // }
  // 确保静态资源能被正确处理
  // assetPrefix: process.env.NODE_ENV === 'prod' ? '.' : '',
  // webpack: (config) => {
  //   config.module.rules.push({
  //     test: /\.less$/,
  //     use: [
  //       'style-loader',
  //       'css-loader',
  //       {
  //         loader: 'less-loader',
  //         options: {
  //           lessOptions: {
  //             modifyVars: {
  //               // 自定义 Ant Design 主题变量
  //               'primary-color': '#1890ff', 
  //             },
  //             javascriptEnabled: true,
  //           },
  //         },
  //       },
  //     ],
  //   });
  //   return config;
  // },
  // fastRefresh: true,
  // concurrentFeatures: true
  
};

const withNextIntl = createNextIntlPlugin('./i18n/request.tsx')(nextConfig);

export default withAntdLess(withNextIntl);
