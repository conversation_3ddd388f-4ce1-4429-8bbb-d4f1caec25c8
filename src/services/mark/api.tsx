import req from '@/utils/request';
import { CreateParams, UpdateParams } from '@/types/mark/index';

//创建标注
export const createMark = ({ agentId, questions, answers }: CreateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/markInfo/create', { agentId, questions, answers });

//编辑标注
export const updateMark = ({ agentId, id, questions, answers }: UpdateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/markInfo/edit', { id, questions, answers, agentId });

//删除标注
export const deleteMark = (ids: number[]): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/markInfo/del', { ids });

//激活标注
export const activateMark = (ids: number[]): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/markInfo/activate', { ids });
