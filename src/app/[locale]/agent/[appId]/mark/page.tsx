'use client';
import React, { useState, useRef, useEffect } from 'react';
import { Card, Button, Space, message, Switch, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import Layout from '@/components/LayoutAgent';
import PublicTable from '@/components/PublicTable';
import AddOrEditMark from './components/addOrEditMark';
import { useParams } from 'next/navigation';
import { deleteMark, activateMark } from '@/services/mark/api';
import { agentDetail } from '@/services/agent/api';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';

const { confirm } = Modal;

interface ListTemplate {
  id: number;
  questions: string;
  answers: string;
  hitCounts: number;
  status: number;
}

const STATUS_MAP: { [key: string]: string } = {
  '1': '未处理',
  '2': '处理中',
  '3': '已应用',
  '4': '处理失败',
  '5': '处理已删除',
};

const AgentMarkPage: React.FC = () => {
  const agentId = useParams().appId;
  const { userInfo } = useStore();
  const [popOpen, setPopOpen] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<ListTemplate | undefined>();
  const [mode, setMode] = useState<'add' | 'edit' | 'see'>('add');
  const [switchChecked, setSwitchChecked] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>({});
  const publicTableRef = useRef<{
    getTableData: () => void;
    selectedRowKeys: any[];
    setSelectedRowKeys: (keys: any[]) => void;
  }>(null);

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '问题',
      dataIndex: 'questions',
      key: 'questions',
      ellipsis: true,
      width: 200,
    },
    {
      title: '答案',
      dataIndex: 'answers',
      key: 'answers',
      ellipsis: true,
      width: 200,
    },
    {
      title: '命中次数',
      dataIndex: 'hitCounts',
      key: 'hitCounts',
      width: 60,
      render: (_, record) => record.hitCounts ?? 0,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 60,
      render: (_, record) => STATUS_MAP[String(record.status)],
    },

    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleSee(record)}>查看</a>
          {roleJudgment(userInfo, 'AGENT_MARK_EDIT') && <a onClick={() => handleEdit(record)}>编辑</a>}
          {roleJudgment(userInfo, 'AGENT_MARK_DELETE') && (
            <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
              删除
            </a>
          )}
        </Space>
      ),
    },
  ];

  const onCancel = () => {
    setPopOpen(false);
  };
  const onSuccess = () => {
    setPopOpen(false);
    refreshTable();
  };

  //刷新列表
  const refreshTable = () => {
    if (publicTableRef.current) {
      publicTableRef.current.getTableData(); // 调用 getTableData 方法
    }
  };

  const addMark = () => {
    setMode('add');
    setPopOpen(true);
    setInitialValues(undefined);
  };
  const handleEdit = (record: ListTemplate) => {
    setMode('edit');
    setPopOpen(true);
    setInitialValues(record);
  };

  const handleSee = (record: ListTemplate) => {
    setMode('see');
    setPopOpen(true);
    setInitialValues(record);
  };

  const handleDelete = (record: ListTemplate) => {
    confirm({
      title: '确认删除',
      content: `确认删除该条记录：${record.questions}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        deleteMark([record.id]).then((res) => {
          if (res.success) {
            message.success('删除成功');
            refreshTable();
          } else {
            message.error(res.msg || '删除失败');
          }
        });
      },
    });
  };
  const switchChange = (checked: boolean) => {
    setSwitchChecked(checked);
    if (publicTableRef.current && checked) {
      if (publicTableRef.current.selectedRowKeys.length === 0) {
        message.warning('请先选择要标注回复的数据');
        return;
      } else {
        confirm({
          title: '提示',
          content: `确认把所选数据标注回复?`,
          okText: '确认',
          cancelText: '取消',
          okButtonProps: {
            danger: true,
          },
          async onOk() {
            activateMark(publicTableRef?.current?.selectedRowKeys!).then((res) => {
              if (res.success) {
                message.success('标注回复成功');
                refreshTable();
                publicTableRef?.current?.setSelectedRowKeys([]);
                setSwitchChecked(false);
              } else {
                message.error(res.msg || '标注回复失败');
              }
            });
          },
          onCancel() {
            setSwitchChecked(false);
          },
        });
      }
    }
  };

  //获取智能体详情
  const getDetail = async () => {
    const res = await agentDetail(Number(agentId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    getDetail();
  }, []);
  return (
    <Layout curActive={`/agent/${agentId}/mark`} detailData={detail}>
      <div>
        <Card variant="borderless">
          <Space
            size="large"
            style={{
              marginBottom: 24,
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <Space size="small">
              标注回复
              <Switch size="small" onChange={switchChange} checked={switchChecked} />
            </Space>
            {roleJudgment(userInfo, 'AGENT_MARK_ADD') && (
              <Button type="primary" icon={<PlusOutlined />} onClick={addMark}>
                添加标注
              </Button>
            )}
          </Space>
          <PublicTable
            ref={publicTableRef}
            columns={columns}
            url={'/ai-manage/v1/markInfo/list'}
            rowKey={'id'}
            defaultQuery={{ agentId }}
            rowSelection={true}
          />
        </Card>
      </div>
      <AddOrEditMark
        open={popOpen}
        initialValues={initialValues}
        mode={mode}
        onCancel={onCancel}
        onSuccess={onSuccess}
        agentId={agentId as string}
      />
    </Layout>
  );
};

export default AgentMarkPage;
