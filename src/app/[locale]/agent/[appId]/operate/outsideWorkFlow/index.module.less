// 主容器样式
.container {
    padding: 24px;
    background: #fbfdfe;
    min-height: calc(100vh - 64px);
}

// 标题样式
.title {
    margin: 0 0 24px 0;
    font-size: 20px;
    font-weight: 600;
    color: #171c1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .titleIcon {
        font-size: 18px;
    }

    .titleText {
        font-weight: 600;
        color: #171c1f;
    }
}

// 主要内容区域
.content {
    display: grid;
    grid-template-columns: 500px 1fr;
    gap: 24px;
    height: calc(100vh - 140px);

    @media (max-width: 1200px) {
        grid-template-columns: 1fr;
        height: auto;
        gap: 16px;
    }
}

// 输入区域卡片
.inputCard {
    height: fit-content;
    border-radius: 12px;
    border: 1px solid #eff1f2;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #8cc6e1;
    }

    :global {
        .ant-card-head {
            border-bottom: 1px solid #eff1f2;
            padding: 0 24px;
            height: 56px;
            background: linear-gradient(135deg, #f5fdff 0%, #fbfdfe 100%);
            border-radius: 12px 12px 0 0;

            .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #1677FF;
                display: flex;
                align-items: center;
                gap: 8px;

                &::before {
                    content: '';
                    width: 4px;
                    height: 16px;
                    background: #1677FF;
                    border-radius: 2px;
                }
            }
        }

        .ant-card-body {
            padding: 24px;
        }
    }
}

// 结果区域
.resultArea {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.resultCard {
    flex: 1;
    border-radius: 12px;
    border: 1px solid #eff1f2;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #8cc6e1;
    }

    :global {
        .ant-card-head {
            border-bottom: 1px solid #eff1f2;
            padding: 0 24px;
            height: 56px;
            background: linear-gradient(135deg, #fbfdfe 0%, #fdfeff 100%);
            border-radius: 12px 12px 0 0;

            .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #171c1f;
                display: flex;
                align-items: center;
                gap: 8px;

                &::before {
                    content: '';
                    width: 4px;
                    height: 16px;
                    background: linear-gradient(135deg, #8c9193 0%, #a7acad 100%);
                    border-radius: 2px;
                }
            }
        }

        .ant-card-body {
            padding: 24px;
            height: calc(100% - 56px);
            overflow: auto;
        }
    }
}

// 运行按钮样式
.runButton {
    margin-top: 24px;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    background: #1677FF;
    border: none;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(22, 119, 255, 0.4);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        background: #dfe3e4;
        box-shadow: none;
        transform: none;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .content {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .title {
        font-size: 18px;
        margin-bottom: 16px;
    }

    .inputCard,
    .resultCard {
        :global {
            .ant-card-head {
                padding: 0 16px;
                height: 48px;

                .ant-card-head-title {
                    font-size: 14px;
                }
            }

            .ant-card-body {
                padding: 16px;
            }
        }
    }

    .runButton {
        height: 40px;
        font-size: 14px;
        margin-top: 16px;
    }
}