.accordion {
  overflow: hidden;
}

.accordionItem {
  border-bottom: 1px solid #ebebeb;
  overflow: hidden;

  &:hover {
    border-color: #d9d9d9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.accordionHeader {
  padding: 15px;
  background-color: #f5f5f5;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s;

  &:hover {
    background-color: #e9e9e9;
  }

  :global {
    .ant-list {
      width: 100%;

      .ant-list-item {
        padding: 0;
        border: none;
      }
    }
  }
}

.accordionIcon {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  transition: color 0.3s ease;

  &:hover {
    color: #1890ff;
  }
}

.accordionContent {
  padding: 15px;
  background-color: #f9f9f9;
  border-top: 1px solid #f0f0f0;
}