import req from '@/utils/requestDify';

//获取运行工作流需要输入的变量
export const getWorkflowVar = (url: string): Promise<any> => req.get(`${url}/v1/parameters`);

//执行 workflow (streaming 模式)
export const runWorkflow = async (url: string, data: any): Promise<any> => {
  const response = await fetch(`${url}/v1/workflows/run`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: data.user || '',
    },
    body: JSON.stringify({ ...data, response_mode: 'streaming' }),
  });

  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

  const reader = response.body?.getReader();
  if (!reader) throw new Error('无法获取响应流');

  return await processStream(reader);
};

// 处理流数据
const processStream = async (reader: ReadableStreamDefaultReader): Promise<any> => {
  const decoder = new TextDecoder();
  let buffer = '';

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';

    for (const line of lines) {
      const result = parseEventLine(line);
      if (result) return result;
    }
  }

  return {};
};

// 解析事件行
const parseEventLine = (line: string): any => {
  console.log(line.startsWith('data: '), line);

  if (!line.trim() || !line.startsWith('data: ')) return null;

  try {
    const jsonData = JSON.parse(line.slice(6));
    return jsonData.event === 'workflow_finished' ? jsonData.data?.outputs || jsonData.data : null;
  } catch {
    return null;
  }
};
