'use client';
import React, { useEffect, useRef } from 'react';
import { <PERSON>, But<PERSON>, Card, message } from 'antd';
import { editRole, getRoleInfo } from '@/services/role/api';
import Layout from '@/components/Layout';
import { useRouter, useParams } from 'next/navigation';
import CommonForm from '../../components/commonForm';

interface FormRef {
  checkedKeys: React.Key[];
  setCheckedKeys: (keys: React.Key[]) => void;
  functionAuth: React.Key[];
  setFunctionAuth: (auth: { [key: number]: number[] }) => void;
  menuTree: any[];
}

const EditRole: React.FC = () => {
  const formRef = useRef<FormRef>(null);
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = useParams();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      editRoleFn(values);
    } catch (error) {
      message.error('有必填项未填写，请检查');
    }
  };

  //编辑角色
  const editRoleFn = async (values: any) => {
    // 获取所有功能权限ID
    const functionAuthIds = Object.values(formRef.current?.functionAuth || {}).flat();
    const { success, msg } = await editRole({
      ...values,
      permissionIds: [...formRef.current?.checkedKeys!, ...functionAuthIds],
      id,
    });
    if (success) {
      message.success('编辑成功');
      router.push('/system/role');
    } else {
      message.error(msg || '创建失败');
    }
  };

  //查询角色详情
  const getRoleInfoFn = async () => {
    const { success, resp, msg } = await getRoleInfo(Number(id));
    if (success) {
      const resData = resp[0];
      form.setFieldsValue(resData);
      //处理数据
      const menuKeys: number[] = [];
      const functionAuthMap: { [key: number]: number[] } = {};

      // 递归获取所有菜单的 key 和功能权限
      const getMenuKeys = (data: any[]) => {
        data.forEach((item) => {
          menuKeys.push(item.key);
          if (item.children?.length) {
            getMenuKeys(item.children);
          }
          // 收集功能权限的 key
          if (item.functionChildren?.length) {
            const functionIds = resData.permissionIds.filter((id: number) =>
              item.functionChildren.some((func: any) => func.key === id),
            );
            if (functionIds.length > 0) {
              functionAuthMap[item.key] = functionIds;
            }
          }
        });
      };

      if (formRef.current) {
        getMenuKeys(formRef.current.menuTree);
        // 区分可见页面和功能权限
        const checkedKeys = resData.permissionIds.filter((id: number) => menuKeys.includes(id));

        formRef.current.setCheckedKeys(checkedKeys);
        formRef.current.setFunctionAuth(functionAuthMap);
      }
    } else {
      message.error(msg || '查询失败');
    }
  };

  const handleCancel = () => {
    router.push('/system/role');
  };

  useEffect(() => {
    getRoleInfoFn();
  }, []);

  return (
    <Layout curActive="/system/role">
      <Card title="编辑角色" variant="borderless">
        <Form form={form} layout="vertical">
          <CommonForm ref={formRef} />
          <Form.Item>
            <div style={{ textAlign: 'right', marginTop: 24 }}>
              <Button onClick={handleCancel} style={{ marginRight: 16 }}>
                取消
              </Button>
              <Button type="primary" onClick={handleSubmit}>
                确定
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </Layout>
  );
};

export default EditRole;
