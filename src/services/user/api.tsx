import req from '@/utils/request';

interface LoginParams {
  account: string;
  password: string;
  kek?: string;
  keiv?: string;
  codeId: string;
  verifyCode: string;
}

export const getImageCode = (): Promise<SERES_RESPONSE> => req.get('/ai-manage/v1/kaptcha/imageCode');

export const login = (params: LoginParams): Promise<SERES_RESPONSE> => {
  const { account, password, kek, keiv, codeId, verifyCode } = params;
  return req.post('/ai-manage/v1/login', {
    account,
    password,
    kek,
    keiv,
    codeId,
    verifyCode,
  });
};

// export const getUserInfo = (id: number): Promise<SERES_RESPONSE> =>
//   req.get(`/ai-manage/v1/user/detail/${id}`);

//退出登录
export const logout = (): Promise<SERES_RESPONSE> => req.get('/ai-manage/v1/logout');

//获取用户个人菜单树
export const getUserList = (): Promise<SERES_RESPONSE> => req.get('/ai-manage/v1/menu/userList');

//查询用户详细信息
export const fetchUserInfo = (): Promise<SERES_RESPONSE> => req.get('/ai-manage/v1/user/info');
