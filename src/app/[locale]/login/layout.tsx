'use client';
import { ReactNode } from 'react';
import { BsRobot } from 'react-icons/bs';
import styles from './login.module.less';

type Props = {
  children: ReactNode;
};

export default function LoginLayout({ children }: Props) {
  return (
    <div className={styles.main}>
      <div className={styles.container}>
        {/* <div>AI技术平台</div> */}
        <div className={styles.title}>
          <div className={styles.icon}>
            <BsRobot />
          </div>
          <span>智能体矩阵</span>
        </div>
        {children}
      </div>
      <div className={styles.copyright}>
        Copyright © {new Date().getFullYear()} ThinkTank. All Rights Reserved. 北京金康赛力斯科技有限公司 版权所有
      </div>
    </div>
  );
}
