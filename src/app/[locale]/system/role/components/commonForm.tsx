'use client';
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Row, Col, Checkbox, message, Table } from 'antd';
import { getTreeList } from '@/services/menu/api';
import type { ListDataType } from '@/types/menu';

const CommonForm = forwardRef((props, ref) => {
  const [menuTree, setMenuTree] = useState<ListDataType[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [functionAuth, setFunctionAuth] = useState<{ [key: number]: number[] }>({});

  useImperativeHandle(ref, () => ({
    checkedKeys,
    setCheckedKeys,
    functionAuth,
    setFunctionAuth,
    menuTree,
  }));
  // 将树形数据转换为表格数据（保持层级关系）
  const getTableData = (data: ListDataType[]): any[] => {
    return data.map((item) => ({
      key: item.key,
      title: item.title,
      functionChildren: item.functionChildren,
      children: item.children?.length ? getTableData(item.children) : undefined,
    }));
  };

  const columns = [
    {
      title: '可见页面',
      dataIndex: 'title',
      key: 'title',
      width: 300,
      render: (text: string, record: any) => (
        <Checkbox
          checked={checkedKeys.includes(record.key)}
          onChange={(e) => {
            const newCheckedKeys = e.target.checked
              ? [...checkedKeys, record.key]
              : checkedKeys.filter((key) => key !== record.key);
            setCheckedKeys(newCheckedKeys);
          }}
        >
          {text}
        </Checkbox>
      ),
    },
    {
      title: '功能权限',
      dataIndex: 'functionChildren',
      key: 'functionChildren',
      render: (_: any, record: any) => {
        const functions = record.functionChildren || [];
        return functions.length > 0 ? (
          <Checkbox.Group
            options={functions.map((func: any) => ({
              label: func.title,
              value: func.key,
            }))}
            value={functionAuth[record.key] || []}
            onChange={(checkedValues) => handleFunctionChange(record.key, checkedValues as number[])}
          />
        ) : null;
      },
    },
  ];

  // 获取菜单树数据
  const getMenuTreeData = async () => {
    const { success, resp, msg } = await getTreeList();
    if (success) {
      setMenuTree(resp);
    } else {
      message.error(msg || '获取菜单树失败');
    }
  };

  // 处理功能权限变化
  const handleFunctionChange = (menuId: number, functions: number[]) => {
    setFunctionAuth((prev) => ({
      ...prev,
      [menuId]: functions,
    }));
  };

  useEffect(() => {
    getMenuTreeData();
  }, []);

  return (
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="角色名称" name="roleName" rules={[{ required: true, message: '请输入角色名称' }]}>
            <Input placeholder="请输入角色名称" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="角色描述" name="description">
            <Input placeholder="请输入角色描述" />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label="权限配置">
        <div
          style={{
            border: '1px solid #d9d9d9',
            padding: '16px',
            borderRadius: '8px',
          }}
        >
          <Table
            columns={columns}
            dataSource={getTableData(menuTree)}
            pagination={false}
            // expandable={{
            //   defaultExpandAllRows: true,
            // }}
          />
        </div>
      </Form.Item>
    </>
  );
});
// 设置显示名称
CommonForm.displayName = 'CommonForm';
export default CommonForm;
