'use client';
import { useState, useEffect } from 'react';
import { Row, Col, Space, Typography } from 'antd';
import Layout from '@/components/Layout';
import KnowledgeSidebar from './components/KnowledgeSidebar';
import KnowledgeDatasets from './components/KnowledgeDatasets';
import KnowledgeTesting from './components/KnowledgeTesting';
import { LeftOutlined } from '@ant-design/icons';

import { useRouter, useSearchParams } from 'next/navigation';
const componentMap = {
  datasets: KnowledgeDatasets,
  testing: KnowledgeTesting,
};
const KonwledgeFilePage: React.FC = () => {
  const router = useRouter();
  const items = [
    {
      key: 'datasets',
      label: '数据集',
    },
    {
      key: 'testing',
      label: '检索测试',
    },
  ];
  const [pageId, setPageId] = useState<string>('datasets');
  const DynamicRenderer = ({ pageId }) => {
    const Component = componentMap[pageId] || KnowledgeDatasets;
    return <Component />;
  };
  const handleSelect = (selectedItem: { key: string }) => {
    setPageId(selectedItem.key);
    localStorage.setItem('pageId', selectedItem.key);
    // 可以在这里添加更多逻辑
  };
  useEffect(() => {
    setPageId(localStorage.getItem('pageId') || 'datasets');
    return () => {
      localStorage.removeItem('pageId');
    };
  }, []);
  return (
    <Layout curActive="/konwledge/datasets">
      <div>
        <Typography.Title level={4} style={{ margin: 0 }}>
          <Space style={{ cursor: 'pointer' }} onClick={() => router.back()}>
            <LeftOutlined />
            返回
          </Space>
        </Typography.Title>
        <Row gutter={[16, 16]}>
          <Col span={4}>
            <KnowledgeSidebar items={items} pageId={pageId} handleSelect={handleSelect} />
          </Col>
          <Col span={20}>
            <DynamicRenderer pageId={pageId} />
          </Col>
        </Row>
      </div>
    </Layout>
  );
};

export default KonwledgeFilePage;
