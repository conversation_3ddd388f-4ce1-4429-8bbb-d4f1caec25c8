'use client';
import React, { memo } from 'react';
import { Form, Input, InputNumber, Select, Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { FormFieldItem, InputVarType, fileType } from './types';
import Cookies from 'js-cookie';

const { TextArea } = Input;
const { Option } = Select;

interface IProps {
  item: FormFieldItem;
  url: string;
}

const FormItem: React.FC<IProps> = ({ item, url }) => {
  // 根据type类型渲染不同的表单控件
  const renderFormControl = () => {
    switch (item.type) {
      case InputVarType.textInput:
        return <Input placeholder={item.placeholder || `请输入${item.label}`} />;

      case InputVarType.paragraph:
        return <TextArea rows={4} placeholder={item.placeholder || `请输入${item.label}`} />;

      case InputVarType.select:
        return (
          <Select placeholder={item.placeholder || `请选择${item.label}`}>
            {item.options?.map((option: any, index: number) => {
              // 处理两种格式：字符串数组 或 对象数组
              if (typeof option === 'string') {
                return (
                  <Option key={index} value={option}>
                    {option}
                  </Option>
                );
              } else {
                return (
                  <Option key={index} value={option.value}>
                    {option.label}
                  </Option>
                );
              }
            })}
          </Select>
        );

      case InputVarType.number:
        return <InputNumber style={{ width: '100%' }} placeholder={item.placeholder || `请输入${item.label}`} />;

      case InputVarType.singleFile:
        return (
          <Upload
            maxCount={1}
            accept={item.allowed_file_types?.map((ext) => fileType[ext]).join(',')}
            action={url + '/v1/files/upload'}
            headers={{ Authorization: Cookies.get('access_token') as string }}
          >
            <Button icon={<UploadOutlined />}>{item.placeholder || `选择文件`}</Button>
          </Upload>
        );

      default:
        return <Input placeholder={item.placeholder || `请输入${item.label}`} />;
    }
  };

  return (
    <Form.Item
      name={item.variable}
      label={item.label}
      rules={[
        {
          required: item.required,
          message: `请输入${item.label}`,
        },
      ]}
      initialValue={item.default}
      tooltip={item.description}
    >
      {renderFormControl()}
    </Form.Item>
  );
};

export default memo(FormItem);
