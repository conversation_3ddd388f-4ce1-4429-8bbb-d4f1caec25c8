import req from '@/utils/request';
interface FlowData {
  id: string;
  content?: any;
  title?: string;
  description?: string;
}
1;
interface TryRunningData {
  id: string;
  variables?: { [key: string]: any };
}
//查询模型列表
export const getAiLlmModelList = (): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiLlmBrand/list?asTree=true`);

//保存工作流
export const flowUpdate = (data: FlowData): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiWorkflow/update`, data);

//查询工作流详情
export const flowDetail = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiWorkflow/detail`, { params: { id } });

//获取工作流自定义参数
export const getRunningParameters = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiWorkflow/getRunningParameters`, { params: { id } });

//运行
export const tryRunning = (data: TryRunningData): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiWorkflow/tryRunning`, data);

//获取插件数据
export const getTinyFlowData = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiPluginTool/getTinyFlowData`, { params: { id } });

//获取大模型列表
export const getLlmList = (): Promise<SERES_LLMS_RESPONSE<any>> => req.get(`/ai-flow/api/v1/aiLlm/list`);
