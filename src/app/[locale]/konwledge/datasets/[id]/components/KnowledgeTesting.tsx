import React, { useEffect, useState } from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { getSearchTest, getList } from '@/services/konwledge/api';
import {
  Card,
  Input,
  Select,
  List,
  Button,
  Form,
  Slider,
  Flex,
  Space,
  message,
  Typography,
  InputNumber,
  Skeleton,
  Popover,
} from 'antd';
import StyleSheet from '../../page.module.less';

interface TableProps {
  id: number;
  summarize: string;
  source: string;
}
const { Option } = Select;
function KnowledgeTesting() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setState] = useState({
    konwOptions: [],
    formData: {
      knowledgeBaseIds: [],
      text: '',
      searchSource: 10,
      weightedRanker: 0.3,
      markWeightedRanker: 0.3,
    },
    tableData: [] as TableProps[],
  });
  const fetchSelectOptions = async (name?: string) => {
    try {
      const response = await getList({ params: { name }, current: 1, pageSize: 100000 });
      if (response.success) {
        response.resp[0].list.forEach((item: any) => {
          item.value = item.id;
          item.label = item.name;
        });
        setState((prev) => ({ ...prev, konwOptions: response.resp[0].list }));
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    }
  };

  const handleSearch = () => {
    // 模拟查询逻辑，实际应替换为API调用
    fetchData();
  };
  const fetchData = async () => {
    if (state.formData.knowledgeBaseIds.length === 0) {
      message.error('请选择测试知识库');
      return;
    }
    try {
      setLoading(true);
      const values = await form.getFieldsValue(true);
      if (!values) return;
      const obj = {
        ...state.formData,
        ...values,
      };
      // 模拟数据获取
      const { resp, success } = await getSearchTest(obj);
      if (success && resp[0] !== null) {
        setState((prevState) => ({
          ...prevState,
          tableData: resp[0],
        }));
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchSelectOptions();
    // fetchData();
  }, []);
  const renderPopover = () => (
    <div>
      <Form
        labelCol={{ span: 12 }}
        wrapperCol={{ span: 12 }}
        form={form}
        initialValues={state.formData}
        onValuesChange={(value) => {
          setState((prev) => ({ ...prev, formData: { ...prev.formData, ...value } }));
        }}
      >
        <Form.Item name="searchSource" label="返回结果" tooltip="检索最大条数,默认10条">
          <InputNumber min="0" max="10" />
        </Form.Item>
        <Form.Item name="markWeightedRanker" label="匹配度评分" tooltip="取值0到1之间(默认0.3)">
          <InputNumber min="0" max="1" step="0.1" />
        </Form.Item>
        <Form.Item name="weightedRanker" label="向量权重" tooltip="向量的权重占比,取值0到1之间(默认0.7)">
          <InputNumber min="0" max="1" step="0.1" />
        </Form.Item>
        {/* <Form.Item label={'autoQuestions'} tooltip={'autoQuestionsTip'}>
              <Flex gap={20} align="center">
                <Flex flex={1}>
                  <Form.Item name={['parser_config', 'auto_questions']} noStyle initialValue={0}>
                    <Slider max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Flex>
                <Form.Item name={['parser_config', 'auto_questions']} noStyle>
                  <InputNumber max={10} min={0} />
                </Form.Item>
              </Flex>
            </Form.Item> */}
        {/* <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
              <Space>
                <Button type="primary">确定</Button>
                <Button htmlType="button">取消</Button>
              </Space>
            </Form.Item> */}
      </Form>
    </div>
  );

  return (
    <div>
      <Card
        variant="borderless"
        style={{
          boxShadow: 'none',
          height: 'calc(100vh - 152px)',
          overflowY: 'auto',
        }}
      >
        <div className={StyleSheet.topbar}>
          <Space.Compact block>
            <Select
              allowClear
              status={state.formData.knowledgeBaseIds.length > 0 ? '' : 'error'}
              style={{ width: '30%' }}
              maxCount={100}
              mode="multiple"
              value={state.formData.knowledgeBaseIds}
              onChange={(value) => {
                setState((prev) => ({ ...prev, formData: { ...prev.formData, knowledgeBaseIds: value } }));
              }}
              placeholder="请选择测试知识库"
              options={state.konwOptions}
            />
            <Input
              style={{ width: '50%' }}
              placeholder="请输入检索测试关键词"
              value={state.formData.text}
              onChange={(e) => setState((prev) => ({ ...prev, formData: { ...prev.formData, text: e.target.value } }))}
            />
            <Popover placement="bottom" content={renderPopover} arrow={false}>
              <Button icon={<SettingOutlined />}></Button>
            </Popover>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
          </Space.Compact>
        </div>
        <List
          loading={loading}
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 4,
            lg: 4,
            xl: 6,
            xxl: 3,
          }}
          dataSource={state.tableData}
          renderItem={(item: TableProps) => (
            <List.Item>
              <Card
                className={StyleSheet['custom-card']}
                classNames={{
                  body: StyleSheet.myActions,
                  header: StyleSheet.myHeader,
                }}
                title={item.id}
                actions={[
                  <Space
                    key={item.source}
                    style={{ display: 'flex', justifyContent: 'space-between', paddingLeft: '15px' }}
                  >
                    <span>评分：{item.source}</span>
                  </Space>,
                ]}
              >
                <Skeleton title={false} loading={loading} active>
                  <Typography.Paragraph ellipsis={{ rows: 2 }}>{item.summarize}</Typography.Paragraph>
                </Skeleton>
              </Card>
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
}

export default KnowledgeTesting;
