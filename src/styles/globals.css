:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --main-1000: #002a36;
  --main-900: #003a51;
  --main-800: #004f69;
  --main-700: #00637f;
  --main-600: #1b7796;
  --main-500: #2c86a8;
  --main-400: #4e99b9;
  --main-300: #6aadcb;
  --main-200: #8cc6e1;
  --main-100: #abe0f7;
  --main-50: #cdf5ff;
  --main-25: #e6faff;
  --main-10: #f5fdff;
  --main-5: #fafcfd;

  --gray-10000: #000000;
  --gray-2000: #0c1214;
  --gray-1000: #171c1f;
  --gray-900: #212729;
  --gray-800: #42484a;
  --gray-700: #616161;
  --gray-600: #8c9193;
  --gray-500: #a7acad;
  --gray-400: #c4c7c8;
  --gray-300: #dfe3e4;
  --gray-200: #eff1f2;
  --gray-100: #f8fafb;
  --gray-50: #fbfdfe;
  --gray-10: #fdfeff;
  --gray-0: #ffffff;

  --main-color: #1c6586;
  --main-color-dark: #004d5c;
  --main-light-1: #0076ab;
  --main-light-2: #daeaed;
  --main-light-3: #edf0f1;
  --main-light-4: #f2f5f5;
  --main-light-5: #f7fafb;
  --main-light-6: #fafdfd;
  --secondry-color: #4e616d;
  --error-color: #ba1a1a;

  --bg-sider: var(--main-5);
  /* --color-text: var(--c-black); */

  --min-width: 400px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
  font-weight: normal;
}
body {
  /* display: flow-root; */
   box-sizing: border-box;
  min-height: 100vh;
  color: rgb(var(--foreground-rgb));
  line-height: 1.6;
  font-family:
    'Roboto',
    'Noto Sans SC',
    'HarmonyOS Sans SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Helvetica Neue',
    Arial,
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

div,
span,
img,
p,
main {
  box-sizing: border-box;
}

.home {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  flex-wrap: wrap;
}

.bytemd {
  height: calc(100vh - 260px) !important;
}
