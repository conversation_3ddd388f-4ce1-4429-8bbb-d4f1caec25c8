'use client';

import React from 'react';
import styles from '../page.module.less';
import { MenuOutlined, PlusOutlined } from '@ant-design/icons';
// import type { StateIprops } from '../page'; // 引用你定义的类型
import { ProChat } from '@ant-design/pro-chat';
import { useTheme } from 'antd-style';
// interface ChatComponentProps {
//   state: StateIprops;
//   setState: React.Dispatch<React.SetStateAction<StateIprops>>;
// }
// <ChatComponentProps>
const ChatComponent: React.FC<any> = ({ state, setState }) => {
  const theme = useTheme();
  return (
    <div className={styles['chat']}>
      <div className={styles['chat-header']}>
        <div className={styles['header__left']}>
          {!state.isSidebarOpen && (
            <div
              onClick={() => setState({ ...state, isSidebarOpen: true })}
              className={`${styles['nav-btn']} ${styles['close']}`}
            >
              <MenuOutlined />
            </div>
          )}
          <div className={`${styles['newchat']} ${styles['nav-btn']}`}>
            <PlusOutlined /> <span className={styles['text']}>新对话</span>
          </div>
        </div>
        <div className={styles['header__right']}>456456</div>
      </div>
      <div className={styles['chat-examples']}></div>
      <div className={styles['chat-box']}>
        <div
          style={{
            backgroundColor: theme.colorBgLayout,
          }}
        >
          <ProChat
            style={{
              height: '80vh',
              width: '100%',
            }}
            request={async (messages) => {
              const response = await fetch('/api/qwen', {
                method: 'POST',
                body: JSON.stringify({ messages: messages }),
              });
              const data = await response.json();
              return new Response(data.output?.text);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatComponent;
