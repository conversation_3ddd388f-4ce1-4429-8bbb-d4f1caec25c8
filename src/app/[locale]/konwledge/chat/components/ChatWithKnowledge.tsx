'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { AiProChat, ChatMessage } from './AiProChat';
import KnowledgeSelector, { KnowledgeBase } from './KnowledgeSelector';
import { getList } from '@/services/konwledge/api';

interface ChatWithKnowledgeProps {
  style?: React.CSSProperties;
  appStyle?: React.CSSProperties;
  helloMessage?: string;
  botAvatar?: string;
}

const ChatWithKnowledge: React.FC<ChatWithKnowledgeProps> = ({ style, appStyle, helloMessage, botAvatar }) => {
  const [chats, setChats] = useState<ChatMessage[]>([]);
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取知识库列表
  const fetchKnowledgeBases = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getList({
        params: {},
        current: 1,
        pageSize: 100, // 获取所有知识库
      });

      if (response.success) {
        const kbList = response.resp[0]?.list || [];
        setKnowledgeBases(kbList);
      } else {
        message.error(response.msg || '获取知识库列表失败');
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时获取知识库列表
  useEffect(() => {
    fetchKnowledgeBases();
  }, [fetchKnowledgeBases]);

  // 处理聊天请求
  const handleRequest = useCallback(
    async (messages: ChatMessage[]): Promise<Response> => {
      try {
        // 构建请求数据
        const requestData = {
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          knowledgeBaseIds: selectedKnowledgeBases, // 使用当前选中的知识库
          // 其他可能需要的参数
          stream: true,
        };

        // 这里替换为你的实际API端点
        const response = await fetch('/api/chat/knowledge', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
      } catch (error) {
        console.error('聊天请求失败:', error);
        throw error;
      }
    },
    [selectedKnowledgeBases],
  );

  // 处理知识库选择变化
  const handleKnowledgeBasesChange = useCallback((selectedIds: number[]) => {
    setSelectedKnowledgeBases(selectedIds);

    // 可以在这里添加额外的逻辑，比如保存到本地存储
    localStorage.setItem('selectedKnowledgeBases', JSON.stringify(selectedIds));
  }, []);

  // 从本地存储恢复选择的知识库
  useEffect(() => {
    const saved = localStorage.getItem('selectedKnowledgeBases');
    if (saved) {
      try {
        const savedIds = JSON.parse(saved);
        if (Array.isArray(savedIds)) {
          setSelectedKnowledgeBases(savedIds);
        }
      } catch (error) {
        console.error('恢复知识库选择失败:', error);
      }
    }
  }, []);

  return (
    <AiProChat
      loading={loading}
      chats={chats}
      onChatsChange={setChats}
      style={style}
      appStyle={appStyle}
      helloMessage={helloMessage}
      botAvatar={botAvatar}
      request={handleRequest}
      // senderFooter={
      //   <KnowledgeSelector
      //     knowledgeBases={knowledgeBases}
      //     selectedKnowledgeBases={selectedKnowledgeBases}
      //     onKnowledgeBasesChange={handleKnowledgeBasesChange}
      //     loading={loading}
      //   />
      // }
    />
  );
};

export default ChatWithKnowledge;
