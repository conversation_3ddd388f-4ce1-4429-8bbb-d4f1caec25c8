@import '@/styles/variables.less';

.name {
  display: flex;
  flex-direction: row;

  .icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgb(99, 0, 255);
    color: #fff;
    font-size: 26px;
    text-align: center;
    line-height: 50px;
  }
}

.sidebarWrapper {
  max-width: 288px;
  flex-direction: column;
  height: calc(100vh - 152px);
  // overflow-y: auto;
  .sidebarTop {
    text-align: center;

    .knowledgeTitle {
      font-size: 16px;
      line-height: 24px;
      font-weight: @fontWeight700;
      margin-bottom: 6px;
    }

    .knowledgeDescription {
      font-size: 12px;
      font-weight: @fontWeight600;
      color: @gray8;
      margin: 0;
      
    }

    padding-bottom: 20px;
  }

  .divider {
    height: 2px;
    background-image: linear-gradient(to right,
        @gray11 0%,
        @gray11 50%,
        transparent 50%);
    background-size: 10px 2px;
    background-repeat: repeat-x;
  }

  .menuWrapper {
    padding-top: 10px;


    .menu {
      border: none;
      font-size: @fontSize16;
      font-weight: @fontWeight600;

      :global(.ant-menu-item) {
        display: flex;
        align-items: center;
      }
    }

    .defaultWidth {
      width: 240px;
    }

    .minWidth {
      width: 50px;
    }

    .menuText {
      color: @gray3;
      font-size: @fontSize14;
      font-weight: @fontWeight700;
    }
  }
}

.cardList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(310px, 1fr));
  gap: 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
}

.EmptyCenter {
  width: 100%;
  display: flex;
  align-items: center;

}

.myActions {
  // color: @gray3;
  // font-size: @fontSize14;
  // font-weight: @fontWeight700;
  // background-color: red;
}


// .ant-card {
//   :global(.myHeader){
//       color: aquamarine;
//   }
// }
// .ant-list-item{
//     :global(.ant-card ) {
//      .ant-card-head{color: aquamarine ;}
//    }
// }


// .ant-card{
//   :global(.ant-card-head) {
//     color: aquamarine !important;
//   }
// }
.topbar {
  display: flex;
  justify-content: space-between;
  padding: 20px 10px;
  box-sizing: border-box;
  box-shadow: '0 1px 4px rgba(0, 21, 41, 0.08)';
  margin-bottom: 60px;
  border-radius: var(--ant-border-radius-lg);
  border: var(--ant-line-width) var(--ant-line-type) var(--ant-color-border-secondary);
}

// .custom-card {
//   :global(.ant-card-head) {
//     color: aquamarine !important;
//   }
// }

// .ant-btn-solid.ant-btn-red {
//   color: #fff;
//   background: red;
// }

// .myHeader {
//   :global(.ant-card-head-wrapper) {
//     font-size: @fontSize14;
//     font-weight: @fontWeight700;
//     background-color: red ;
//   }
// }