import req from '@/utils/request';
import { CreateMenuData, UpdateMenuData, UpdateFunctionData } from '@/types/menu/index';

//获取全部菜单树
export const getTreeList = (): Promise<SERES_RESPONSE> => req.get(`/ai-manage/v1/menu/treeList`);

//添加菜单
export const addMenu = (data: CreateMenuData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/menu/save', { ...data });

//编辑菜单
export const editMenu = (data: UpdateMenuData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/menu/update', { ...data });

//删除菜单
export const deleteMenu = (id: number): Promise<SERES_RESPONSE> => req.post(`/ai-manage/v1/menu/delete/${id}`);

//设置权限
export const updateFunction = ({ functionAuthoritys, parent, level }: UpdateFunctionData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/menu/updateFunction', {
    functionAuthoritys,
    parent,
    level,
  });
