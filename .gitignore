# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

package-lock.json
# typescript
*.tsbuildinfo
deploy.sh

# Ignore all files in src/app/[locale]/(demo) directory
/src/app/[locale]/(demo)/**

.stylelintcache


# Explicitly include specific lock files if needed
!package-lock.json
!yarn.lock
!pnpm-lock.yaml