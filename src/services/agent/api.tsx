import req from '@/utils/request';
import {
  CreateParams,
  UpdateParams,
  ListParams,
  AgentParams,
  VariableItem,
  AgentLogMessage,
} from '@/types/agent/index';

export const createAgent = ({
  name,
  description = '',
  isPublic,
  config,
  avatar,
}: CreateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/create', { name, description, isPublic, config, avatar });

export const updateAgent = ({
  agentId,
  name,
  description = '',
  isPublic,
  config,
  avatar,
}: UpdateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/edit', { agentId, name, description, isPublic, config, avatar });

export const getAgentList = ({ current, pageSize, params: { search, isMine } }: ListParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/list', {
    current,
    pageSize,
    params: { search, isMine },
  });

export const deleteAgent = (agentId: number): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/del', { agentId });

//查看agent详情
export const agentDetail = (id: number): Promise<SERES_RESPONSE> =>
  req.get(`/ai-manage/v1/agent/detail`, { params: { id } });

//查Agent来源
export const agentType = (): Promise<SERES_RESPONSE> => req.get(`/ai-manage/v1/agent/source`);

//查询Agent应用类型
export const applyType = (): Promise<SERES_RESPONSE> => req.get(`/ai-manage/v1/agent/apply-type`);

//测试连接
export const testConnect = (data: any): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/testConnect', data);

//保存Agent配置

export const saveConfig = ({ agentId, type, config }: AgentParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/config', { agentId, type, config });

//查Agent配置参数
export const getConfigParam = ({
  agentType,
  // url,
  // agentId,
}: {
  agentType: string;
  // url: string;
  // agentId: string;
}): Promise<SERES_RESPONSE> => req.get(`/ai-manage/v1/agent/configParam`, { params: { agentType } });

// //添加agent 知识库
export const saveKnow = ({
  agentId,
  knowledgeIdList,
}: {
  agentId: number;
  knowledgeIdList: number[];
}): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/knowledge', { agentId, knowledgeIdList });

//删除关联的知识库
export const delKnowledge = ({
  agentId,
  knowledgeId,
}: {
  agentId: number;
  knowledgeId: number;
}): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/delKnowledge', { agentId, knowledgeId });

//agent 获取agent URL
export const getAgentApi = (agentId: number): Promise<SERES_RESPONSE> =>
  req.get(`/ai-manage/v1/agent/getAgentApi`, { params: { agentId } });

//查Agent基本信息
export const getBaseinfo = (id: number): Promise<SERES_RESPONSE> =>
  req.get(`/ai-manage/v1/agent/baseinfo`, { params: { id } });

//添加配置Agent变量
export const batchAddVariable = ({
  agentId,
  variableList,
}: {
  agentId: number;
  variableList: VariableItem[];
}): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/batchAddVariable', { agentId, variableList });

//批量修改配置Agent变量
// export const batchUpdateVariable = ({
//   agentId,
//   variableList,
// }: {
//   agentId: number;
//   variableList: variableList[];
// }): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/batchUpdateVariable', { agentId, variableList });

//删除配置Agent变量
// export const delVariable = ({
//   agentId,
//   variableName,
//   variableValue,
// }: {
//   agentId: number;
//   variableName: string;
//   variableValue: string;
// }): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/agent/delVariable', { agentId, variableName, variableValue });

//查询变量
export const getVariables = (agentId: number): Promise<SERES_RESPONSE> =>
  req.get(`/ai-manage/v1/agent/getVariables`, { params: { agentId } });

//优化Agent提示词
export const optimizePrompt = (prompt: string): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/optimizePrompt', { prompt });

//配置Agent提示词
export const savePrompt = ({ agentId, prePrompt }: { agentId: number; prePrompt: string }): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/agent/prompt', { agentId, prePrompt });

//日志对话消息
export const getAgentMessage = ({ current, pageSize, conversationId }: AgentLogMessage): Promise<SERES_RESPONSE> =>
  req.get('/ai-manage/v1/agent/conversation/message', {
    params: {
      current,
      pageSize,
      conversationId,
    },
  });
