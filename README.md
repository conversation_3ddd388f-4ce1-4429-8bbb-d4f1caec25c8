<p align="center">
  <a href="https://nextjs.org">
    <picture>
      <source media="(prefers-color-scheme: dark)" srcset="./tt.png">
    </picture>
    <h1 align="center">Seres-ai-nexus-pro</h1>
  </a>
</p>

<div align="center">

一款基于 Nextjs 最新版本 + antd5.0 的中后台管理系统.

</div>

## 1.0 版本! 🎉🎉🎉

Seres-ai-nexus-pro 1.0.0

## 使用

### 安装

```shell
$ <NAME_EMAIL>:seres-ai/seres-ai-nexus-pro.git
```

安装依赖:

```shell
$ cd seres-ai-nexus-pro && npm install
```

project start:

```shell
$ npm run dev
```

本地构建：project build:

```shell
$ npm run build
```

本地部署：project start:

该情况下启动，需要.next 文件夹（文件夹太大，1 个 G），构建完直接启动就行

```shell
$ npm run start
```

部署服务器构建：project build:

```shell
$ npm run build:test
```

部署服务器启动：project start:

部署服务器需只需要.next/standalone 文件夹，进入该文件夹执行 npm run start:standalone 即可启动

```shell
$ npm run start:standalone
```

## 项目结构

```
├── public
├── src
│   ├── assets
│   ├── components
│   ├── config
│   ├── layouts
│   ├── pages
│   ├── services
│   ├── store
│   ├── styles
│   ├── utils
│   ├── App.tsx

## 浏览器支持

现代主流浏览器.
```

=== 迭代 1 (改进: 0.44) ===
