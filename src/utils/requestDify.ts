import axios from 'axios';
import { notification } from 'antd';
import Cookies from 'js-cookie';

const clearUserInfo = () => {
  Cookies.remove('access_token');
  window && (location.href = '/login');
};

const openNotification = (message: string) => {
  notification.destroy();
  notification.error({
    message: `错误提示：`,
    description: message || '系统繁忙，请稍后再试',
  });
};

const instance = axios.create({
  // baseURL: '',
  timeout: 300000,
});

instance.interceptors.request.use(
  (config) => {
    const access_token = Cookies.get('access_token');
    if (access_token) {
      config.headers['Authorization'] = access_token;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

/**
 * 设置响应拦截器
 */
instance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const {
      response,
      response: { status, data },
    } = error;
    if (error && response) {
      let msg = '';
      if (status === 401 || status === 403) {
        msg = '您的账号登录超时或已在其他地方登录，若不是本人操作，请注意账号安全！';
        clearUserInfo();
      } else if (status === 500) {
        console.log('ddd');
        msg = data.msg || data.msgCode;
      }
      openNotification(msg);

      return Promise.resolve({
        success: false,
        msg: (data && data.msg) || `接口请求出错${status}，请联系管理员！`,
      });
    } else {
      return Promise.resolve({
        success: false,
        msg: '接口请求出错，请联系管理员！',
      });
    }
  },
);

export default instance;
