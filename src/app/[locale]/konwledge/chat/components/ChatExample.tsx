'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { AiProChat, ChatMessage } from './AiProChat';
import KnowledgeSelector, { KnowledgeBase } from './KnowledgeSelector';

// 模拟知识库数据
const mockKnowledgeBases: KnowledgeBase[] = [
  { id: 1, name: '产品手册', content: '产品相关文档' },
  { id: 2, name: '技术文档', content: '技术规范文档' },
  { id: 3, name: '用户指南', content: '用户使用指南' },
  { id: 4, name: '常见问题', content: 'FAQ文档' },
];

const ChatExample: React.FC = () => {
  const [chats, setChats] = useState<ChatMessage[]>([]);
  const [knowledgeBases] = useState<KnowledgeBase[]>(mockKnowledgeBases);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<number[]>([1, 2]);
  const [loading, setLoading] = useState(false);

  // 模拟聊天请求
  const handleRequest = useCallback(
    async (messages: ChatMessage[]): Promise<Response> => {
      console.log('发送消息:', messages);
      console.log('使用的知识库:', selectedKnowledgeBases);

      // 模拟流式响应
      const stream = new ReadableStream({
        start(controller) {
          const encoder = new TextEncoder();
          const responses = [
            '根据您选择的知识库，',
            '我为您查找到以下信息：\n\n',
            `当前使用的知识库包括：${knowledgeBases
              .filter((kb) => selectedKnowledgeBases.includes(kb.id))
              .map((kb) => kb.name)
              .join('、')}\n\n`,
            '这是一个模拟的AI回复，',
            '展示了知识库选择功能的工作原理。',
            '\n\n您可以通过底部的知识库选择器来选择不同的知识库。',
          ];

          let index = 0;
          const interval = setInterval(() => {
            if (index < responses.length) {
              controller.enqueue(encoder.encode(responses[index]));
              index++;
            } else {
              clearInterval(interval);
              controller.close();
            }
          }, 200);
        },
      });

      return new Response(stream);
    },
    [selectedKnowledgeBases, knowledgeBases],
  );

  // 处理知识库选择变化
  const handleKnowledgeBasesChange = useCallback((selectedIds: number[]) => {
    setSelectedKnowledgeBases(selectedIds);
    message.success(`已选择 ${selectedIds.length} 个知识库`);

    // 保存到本地存储
    localStorage.setItem('selectedKnowledgeBases', JSON.stringify(selectedIds));
  }, []);

  // 从本地存储恢复选择
  useEffect(() => {
    const saved = localStorage.getItem('selectedKnowledgeBases');
    if (saved) {
      try {
        const savedIds = JSON.parse(saved);
        if (Array.isArray(savedIds)) {
          setSelectedKnowledgeBases(savedIds);
        }
      } catch (error) {
        console.error('恢复知识库选择失败:', error);
      }
    }
  }, []);

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
        <h2>AiProChat 知识库功能演示</h2>
        <p>这是一个演示新架构的示例，知识库逻辑由父组件管理，通过 senderFooter 传递给 AiProChat。</p>
      </div>

      <div style={{ flex: 1 }}>
        <AiProChat
          loading={loading}
          chats={chats}
          onChatsChange={setChats}
          helloMessage="您好，我是智能助手，请选择知识库并开始对话"
          request={handleRequest}
          // senderFooter={
          //   <KnowledgeSelector
          //     knowledgeBases={knowledgeBases}
          //     selectedKnowledgeBases={selectedKnowledgeBases}
          //     onKnowledgeBasesChange={handleKnowledgeBasesChange}
          //     loading={loading}
          //     placeholder="选择要使用的知识库"
          //   />
          // }
        />
      </div>
    </div>
  );
};

export default ChatExample;
