'use client';
import React, { useState, useEffect, memo, forwardRef, useImperativeHandle } from 'react';
import { Table, message } from 'antd';
import { setStorage, getStorage } from '@/utils/storage';
import zhCN from 'antd/es/locale/zh_CN';
import request from '@/utils/request';

interface IProps {
  columns: any[];
  defaultQuery?: any;
  url: string;
  rowKey: string;
  isCatch?: boolean; // 是否记录查询参数
  rowSelection?: boolean | {}; // 是否需要多选
}

const getPageStorage = (key: string): any => {
  const res = getStorage(window.location.hash);
  if (res && typeof res === 'object' && key in res) {
    return res[key as keyof typeof res];
  }
  return null;
};

const PublicTable = forwardRef((props: IProps, ref) => {
  const { columns, defaultQuery = {}, url, rowKey, isCatch = false, rowSelection = false } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [current, setCurrent] = useState<number>(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1,
  );
  const [pageSize, setPageSize] = useState<number>(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10,
  );
  const [total, setTotal] = useState<number>(0);
  const [tableQuery, setTableQuery] = useState<string>(JSON.stringify(defaultQuery));

  const PageChange = (current: number, pageSize: number) => {
    setCurrent(current);
    setPageSize(pageSize);
    if (isCatch) {
      // 翻页时也存储查询参数
      const obj = getStorage(window.location.hash) || {};
      const objAlias = {
        ...obj,
        hash: window.location.hash,
        pagination: { current, pageSize },
      };

      setStorage(objAlias);
    }
  };

  const getTableData = async () => {
    try {
      setLoading(true);
      const queryParams = isCatch && getPageStorage('Query') ? getPageStorage('Query') : defaultQuery;
      const params = { pageSize, current, params: { ...queryParams } };
      const response = await (request.post(url, {
        ...params,
      }) as Promise<SERES_RESPONSE>);
      if (response.success) {
        const Dt = response.resp[0];
        if (Dt.list.length === 0 && Dt.pagination.current > 1) {
          //若查询没有数据，则往前查一页
          setCurrent(current - 1);
        } else {
          setDataSource(Dt.list || []);
          setTotal(Dt.pagination.total || 0);
        }
      } else {
        message.error(response.msg || '查询失败');
      }
      setLoading(false);
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setTableQuery(JSON.stringify(defaultQuery));
  }, [defaultQuery]);

  useEffect(() => {
    if (Object.keys(defaultQuery).indexOf('_t') !== -1) {
      if (current !== 1) {
        setCurrent(1);
      } else {
        getTableData();
      }
    } else {
      getTableData();
    }
  }, [tableQuery]);

  useEffect(() => {
    getTableData();
  }, [current, pageSize]);

  const resetCurrentAndPageSize = () => {
    setCurrent(1);
    setPageSize(10);
  };

  useImperativeHandle(ref, () => ({
    getTableData,
    selectedRowKeys,
    selectedRows,
    setSelectedRowKeys,
    setSelectedRows,
    resetCurrentAndPageSize,
  }));

  const InforData = {
    rowKey,
    dataSource,
    loading,
    columns,
    pagination: {
      pageSize,
      onChange: PageChange,
      current,
      total,
      showTotal: () => `共${total}条，${pageSize}条/页`,
      showSizeChanger: true,
      showQuickJumper: true,
      onShowSizeChange: PageChange,
      locale: zhCN.Pagination, // 设置分页的本地化为中文
    },
    rowSelection:
      typeof rowSelection === 'object'
        ? {
            ...rowSelection,
            preserveSelectedRowKeys: true, // 保留选中状态
          }
        : rowSelection
          ? {
              selectedRowKeys,
              selectedRows,
              preserveSelectedRowKeys: true, // 保留选中状态
              onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
              },
            }
          : undefined,
  };

  return <Table {...InforData} />;
});

// 手动设置显示名称
PublicTable.displayName = 'PublicTable';
export default memo(PublicTable);
