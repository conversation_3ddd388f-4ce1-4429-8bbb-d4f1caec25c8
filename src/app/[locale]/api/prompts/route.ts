import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const name = searchParams.get('name');

  // 这里是示例数据，实际应该从数据库获取
  const mockData = [
    {
      id: '1',
      name: '通用翻译模板',
      content: '请将以下内容翻译成{targetLanguage}：{content}',
      type: '翻译类',
      createTime: '2024-03-20',
      author: '张三',
    },
    {
      id: '2',
      name: '代码优化模板',
      content: '请帮我优化以下代码，并说明优化原因：{code}',
      type: '开发类',
      createTime: '2024-03-21',
      author: '李四',
    },
  ];

  // 根据名称筛选
  const filteredData = name ? mockData.filter((item) => item.name.includes(name)) : mockData;

  return NextResponse.json({
    code: 0,
    message: 'success',
    data: filteredData,
  });
}
