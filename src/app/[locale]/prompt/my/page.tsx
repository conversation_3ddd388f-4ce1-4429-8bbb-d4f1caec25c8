'use client';
import Layout from '@/components/Layout';
import { SearchOutlined } from '@ant-design/icons';
import { Button, Card, Input, message, Modal, Space, Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
// import PromptModal from './components/PromptModal';
import { deleteMyPropmt, getPromptList } from '@/services/prompt/api';
import useStore from '@/store/useStore';
import { roleJudgment } from '@/utils/index';
import zhCN from 'antd/es/locale/zh_CN';
import Link from 'next/link';

const { Title } = Typography;
const { confirm } = Modal;

interface PromptTemplate {
  id: number;
  output: string;
  createTime: string;
  input: string;
  author: string;
  [propKey: string]: any;
}
interface QueryParams {
  params?: string;
  current?: number;
  pageSize?: number;
}
export default function PromptPage() {
  const { userInfo } = useStore();
  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PromptTemplate[]>([]);
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);

  const [pageSize, setPageSize] = useState<number>(10);
  const columns: ColumnsType<any> = [
    {
      title: '问题',
      dataIndex: 'input',
      key: 'input',
      width: 200,
    },
    {
      title: '提示词',
      dataIndex: 'output',
      key: 'output',
      ellipsis: true,
      width: 400,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space size="middle">
          <Link
            href={{
              pathname: '/prompt/optimize',
              query: { id: record.id },
            }}
          >
            优化
          </Link>
          <Link
            href={{
              pathname: '/prompt/assessment',
              query: { id: record.id },
            }}
          >
            评估
          </Link>
          <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
            删除
          </a>
          {/* {roleJudgment(userInfo, 'PROMPT_EDIT') && <a onClick={() => handleEdit(record)}>编辑</a>} */}
        </Space>
      ),
    },
  ];

  const handleSearch = async (params: QueryParams = {}) => {
    try {
      setLoading(true);
      const { resp, message, success } = await getPromptList(params).finally(() => {
        setLoading(false);
      });
      if (success && resp.length > 0) {
        setData(resp[0]?.list);
        setTotal(resp[0]?.pagination?.total);
      } else {
        message.error(message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    }
  };

  const handleDelete = (record: PromptTemplate) => {
    confirm({
      title: '确认删除',
      content: `是否删除该模板：${record.input}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        try {
          const { success, msg } = await deleteMyPropmt({ id: record.id });
          if (success) {
            // TODO: 实现删除逻辑
            message.success(msg);
            handleSearch({ params: searchName, current, pageSize }); // 刷新列表
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };
  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };
  // 初始化加载
  useEffect(() => {
    handleSearch({ params: searchName, current, pageSize });
  }, [searchName, current, pageSize]);
  return (
    <Layout curActive="/prompt/my">
      <div className="p-6">
        <Title level={2} style={{ marginBottom: 24 }}>
          我的提示词
        </Title>
        <Card bordered={false}>
          <div style={{ marginBottom: 24 }}>
            <Space>
              <Input
                placeholder="请输入提示词"
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                style={{ width: 240 }}
                allowClear
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => handleSearch({ params: searchName, current, pageSize })}
              >
                查询
              </Button>
            </Space>
          </div>
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1300 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              defaultPageSize: 10,
              locale: zhCN.Pagination,
              pageSize,
              onChange: PageChange,
              current,
              onShowSizeChange: PageChange,
              total, // 👈 这里传入 total
            }}
          />
        </Card>
      </div>
    </Layout>
  );
}
