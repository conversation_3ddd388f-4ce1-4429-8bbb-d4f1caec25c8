'use client';
import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Upload, message, Collapse } from 'antd';
import { SendOutlined, UploadOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import type { CollapseProps } from 'antd';
import Cookies from 'js-cookie';
import { detail } from '@/services/flow/api';
import ReactJsonView from '@/components/JsonView';
import { sortNodes } from './workUtil'
import { log } from 'node:console';

interface IProps {
  id: string;
  open: boolean;
  onClose: () => void;
  parameters: any;
}

// 步骤状态枚举
enum StepStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 步骤数据接口
interface StepData {
  key: string;
  label: string;
  status: StepStatus;
  result?: any;
  error?: string;
  original: any;
}

const WorkResultDrawer: React.FC<IProps> = ({ id, open, onClose, parameters }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [executeResult, setExecuteResult] = useState<object>({});
  const [collapseItems, setCollapseItems] = useState<any[]>([]);
  const [stepResults, setStepResults] = useState<Record<string, StepData>>({});

  // 获取状态图标
  const getStatusIcon = (status: StepStatus) => {
    switch (status) {
      case StepStatus.SUCCESS:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case StepStatus.ERROR:
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case StepStatus.RUNNING:
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      default:
        return <SendOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 动态生成折叠面板项
  const generateCollapseItems = (): CollapseProps['items'] => {
    return collapseItems.map((item) => {
      const stepData = stepResults[item.key] || { status: StepStatus.PENDING };
      const result = stepData.result || {};

      return {
        key: item.key,
        label: item.label,
        children: (
          <div>
            {stepData.status === StepStatus.ERROR && stepData.error && (
              <div style={{ color: '#ff4d4f', marginBottom: 10 }}>
                错误信息: {stepData.error}
              </div>
            )}
            <ReactJsonView value={result} minHeight={100} background={'#ffffff'} />
          </div>
        ),
        extra: getStatusIcon(stepData.status),
      };
    });
  };

  // 处理流式响应
  const handleStreamResponse = async (response: Response) => {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('无法获取响应流');

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmedLine = line.trim();
          
          if (!trimmedLine || !trimmedLine.startsWith('data: ')) continue;

          const dataMessage = trimmedLine.replace('data: ', '').trim();
          if (!dataMessage || dataMessage === '[DONE]') continue;

          try {
            const parsed = JSON.parse(dataMessage);
            console.log('Parsed stream data:', parsed);

            // 处理不同类型的事件
            if (parsed.event === 'node_started' || parsed.event === 'node_start') {
              // 节点开始执行
              const nodeId = parsed.nodeId || parsed.node_id || parsed.id;
              const nodeName = parsed.nodeName || parsed.node_name || parsed.name || nodeId;
              console.log('节点开始执行:', nodeId, nodeName);

              setStepResults(prev => ({
                ...prev,
                [nodeId]: {
                  ...prev[nodeId],
                  key: nodeId,
                  label: nodeName,
                  status: StepStatus.RUNNING,
                }
              }));
            } else if (parsed.event === 'node_finished' || parsed.event === 'node_complete') {
              // 节点执行完成
              const nodeId = parsed.nodeId || parsed.node_id || parsed.id;
              const nodeName = parsed.nodeName || parsed.node_name || parsed.name || nodeId;
              const result = parsed.data || parsed.result || parsed.output || {};
              console.log('节点执行完成:', nodeId, nodeName, result);

              setStepResults(prev => ({
                ...prev,
                [nodeId]: {
                  ...prev[nodeId],
                  key: nodeId,
                  label: nodeName,
                  status: StepStatus.SUCCESS,
                  result: result,
                }
              }));
            } else if (parsed.event === 'node_error' || parsed.event === 'node_failed') {
              // 节点执行出错
              const nodeId = parsed.nodeId || parsed.node_id || parsed.id;
              const nodeName = parsed.nodeName || parsed.node_name || parsed.name || nodeId;
              const error = parsed.error || parsed.message || '执行出错';
              console.log('节点执行出错:', nodeId, nodeName, error);

              setStepResults(prev => ({
                ...prev,
                [nodeId]: {
                  ...prev[nodeId],
                  key: nodeId,
                  label: nodeName,
                  status: StepStatus.ERROR,
                  error: error,
                }
              }));
            } else if (parsed.event === 'workflow_finished' || parsed.event === 'workflow_complete') {
              // 工作流执行完成
              const result = parsed.data || parsed.result || parsed.output || {};
              console.log('工作流执行完成:', result);
              setExecuteResult(result);
              message.success('工作流执行完成');
            } else if (parsed.event === 'workflow_error' || parsed.event === 'workflow_failed') {
              // 工作流执行出错
              const error = parsed.error || parsed.message || '未知错误';
              console.log('工作流执行出错:', error);
              message.error('工作流执行失败: ' + error);
            } else {
              // 处理其他可能的事件类型
              console.log('未处理的事件类型:', parsed.event, parsed);

              // 如果有节点相关数据，尝试更新步骤状态
              if (parsed.nodeId || parsed.node_id || parsed.id) {
                const nodeId = parsed.nodeId || parsed.node_id || parsed.id;
                const nodeName = parsed.nodeName || parsed.node_name || parsed.name || nodeId;
                const result = parsed.data || parsed.result || parsed.output;

                if (result) {
                  setStepResults(prev => ({
                    ...prev,
                    [nodeId]: {
                      ...prev[nodeId],
                      key: nodeId,
                      label: nodeName,
                      status: StepStatus.SUCCESS,
                      result: result,
                    }
                  }));
                }
              }

              // 如果有工作流结果数据，更新执行结果
              if (parsed.data || parsed.result || parsed.output) {
                const result = parsed.data || parsed.result || parsed.output;
                setExecuteResult(result);
              }
            }
          } catch (jsonError) {
            console.warn('跳过非JSON数据:', dataMessage);
          }
        }
      }
    } catch (error) {
      console.error('Stream processing error:', error);
      message.error('处理流式响应时出错');
    }
  };

  const onFinish = async (values: any) => {
    setSubmitLoading(true);

    // 重置步骤状态
    const initialStepResults: Record<string, StepData> = {};
    collapseItems.forEach(item => {
      initialStepResults[item.key] = {
        key: item.key,
        label: item.label,
        status: StepStatus.PENDING,
        original: item.original,
      };
    });
    setStepResults(initialStepResults);
    setExecuteResult({});

    try {
      // 直接调用流式接口
      const response = await fetch(`${process.env.NEXT_PUBLIC_AI_BASE_URL}/ai-flow/api/v1/aiWorkflow/tryRunningStream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': Cookies.get('access_token') || '',
        },
        body: JSON.stringify({
          id: id,
          variables: values,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await handleStreamResponse(response);
    } catch (error) {
      console.error('Execution error:', error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  const onFinishFailed = (errorInfo: any) => {
    setSubmitLoading(false);
    message.error('失败：' + errorInfo);
    console.log('Failed:', errorInfo);
  };

  const getParameters = async () => {
    const resp = await detail(id);
    const nodeJson = JSON.parse(resp.data?.content);
    const nodes = sortNodes(nodeJson)
    setCollapseItems(nodes)

    // 初始化步骤状态
    const initialStepResults: Record<string, StepData> = {};
    nodes.forEach((node: any) => {
      initialStepResults[node.key] = {
        key: node.key,
        label: node.label,
        status: StepStatus.PENDING,
        original: node.original,
      };
    });
    setStepResults(initialStepResults);

    console.log('工作流节点数据:', nodeJson, nodes)
    console.log('初始化步骤状态:', initialStepResults)
  };

  useEffect(() => { getParameters() }, [id])

  return (
    <Drawer width={640} title="请输入参数" placement="right" closable={false} onClose={onClose} open={open}>
      <Form
        form={form}
        name="basic"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        {parameters &&
          parameters.map((item: any) => {
            let inputComponent: React.ReactNode;
            switch (item.dataType) {
              case 'File':
                inputComponent = (
                  <Upload
                    maxCount={1}
                    action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
                    headers={{ Authorization: Cookies.get('access_token') as string }}
                    onChange={({ file }) => {
                      if (file.status === 'done') {
                        let url = file.response?.response.url;
                        if (url.indexOf('http') < 0) {
                          url = window.location.origin + url;
                        }
                        form.setFieldsValue({
                          [item.name]: url,
                        });
                      }
                    }}
                  >
                    <Button icon={<UploadOutlined />}>上传</Button>
                  </Upload>
                );
                break;
              default:
                inputComponent = <Input />;
            }

            return (
              <Form.Item
                key={item.name}
                label={item.name}
                name={item.name}
                rules={[{ required: item.required }]}
                extra={item.description}
              >
                {inputComponent}
              </Form.Item>
            );
          })}

        <Form.Item wrapperCol={{ offset: 4, span: 18 }}>
          <Button disabled={submitLoading} loading={submitLoading} type="primary" htmlType="submit">
            <SendOutlined /> 开始运行
          </Button>
        </Form.Item>
      </Form>

      <div>
        <div>执行结果：</div>
        <div style={{ padding: 10, backgroundColor: '#F5F6F9' }}>
          <ReactJsonView value={executeResult} minHeight={100} />
        </div>
      </div>

      <div style={{ marginTop: 20 }}>
        <div>执行步骤：</div>
        <div>
          <Collapse
            items={generateCollapseItems()}
            defaultActiveKey={collapseItems.length > 0 ? [collapseItems[0].key] : []}
            onChange={onChange}
            size="small"
          />
        </div>
      </div>
    </Drawer>
  );
};

export default WorkResultDrawer;
