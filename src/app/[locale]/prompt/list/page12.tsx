'use client';
import { useState, useEffect, useRef } from 'react';
import { Card, Input, Button, Table, Space, message, Typography, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import PromptModal from './components/PromptModal';
import { query, deletePropmt } from '@/services/prompt/api';
import zhCN from 'antd/es/locale/zh_CN';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';

const { Title } = Typography;
const { confirm } = Modal;

interface PromptTemplate {
  id: number;
  name: string;
  content: string;
  type: string;
  createTime: string;
  author: string;
}

export default function PromptPage() {
  const { userInfo } = useStore();
  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PromptTemplate[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Partial<PromptTemplate>>();
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');
  const [current, setCurrent] = useState<number>(1);

  const [pageSize, setPageSize] = useState<number>(10);

  useEffect(() => {
    handleSearch();
  }, []);

  const columns: ColumnsType<any> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '模板内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      width: 400,
    },
    {
      title: '模板类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleView(record)}>查看</a>
          {roleJudgment(userInfo, 'PROMPT_EDIT') && <a onClick={() => handleEdit(record)}>编辑</a>}
          {roleJudgment(userInfo, 'PROMPT_DELTE') && (
            <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
              删除
            </a>
          )}
        </Space>
      ),
    },
  ];

  const handleSearch = async () => {
    try {
      setLoading(true);
      const obj = Object.assign({
        params: { name: searchName },
        current,
        pageSize,
      });
      const { resp, message, success } = await query(obj).finally(() => {
        setLoading(false);
      });
      if (success && resp.length > 0) {
        setData(resp[0]?.list);
      } else {
        message.error(message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    }
  };

  const handleAdd = () => {
    setModalMode('add');
    setCurrentRecord(undefined);
    setModalOpen(true);
  };

  const handleEdit = (record: PromptTemplate) => {
    setModalMode('edit');
    setCurrentRecord({ ...record });
    setModalOpen(true);
  };

  const handleDelete = (record: PromptTemplate) => {
    confirm({
      title: '确认删除',
      content: `是否删除该模板：${record.name}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        try {
          const { success, msg } = await deletePropmt({ id: record.id });
          if (success) {
            // TODO: 实现删除逻辑
            message.success(msg);
            handleSearch(); // 刷新列表
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };
  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
    handleSearch();
  };
  const handleView = (record: PromptTemplate) => {
    setModalMode('view');
    setCurrentRecord({ ...record });
    setModalOpen(true);
  };

  const handleModalSuccess = () => {
    setModalOpen(false);
    handleSearch();
  };

  return (
    <Layout curActive="/prompt/list">
      <div className="p-6">
        <Title level={2} style={{ marginBottom: 24 }}>
          模板管理
        </Title>
        <Card bordered={false}>
          <div style={{ marginBottom: 24 }}>
            <Space>
              <Input
                placeholder="请输入模板名称"
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                style={{ width: 240 }}
                allowClear
              />
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                查询
              </Button>
              {roleJudgment(userInfo, 'PROMPT_ADD') && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增
                </Button>
              )}
            </Space>
          </div>
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1300 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              defaultPageSize: 10,
              locale: zhCN.Pagination, // 设置分页的本地化为中文
              pageSize,
              onChange: PageChange,
              current,
              onShowSizeChange: PageChange,
            }}
          />
        </Card>

        <PromptModal
          open={modalOpen}
          mode={modalMode}
          initialValues={currentRecord}
          onCancel={() => setModalOpen(false)}
          onSuccess={handleModalSuccess}
        />
      </div>
    </Layout>
  );
}
