// 卡片主体样式
.card-body {
    display: flex;
    padding: 0;

    .card-text-content {
        .card-header-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-top: 2px;
            margin-bottom: 10px;
        }

        .card-description-text {
            margin: 0;
            color: #8c8c8c;
            font-size: 14px;
            line-height: 1.6;
            height: auto;
            min-height: 44px;
        }
    }
}

.custom-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .custom-card-actions {
        border-top: none !important;
        background: linear-gradient(180deg, rgba(222, 237, 252, 0.5) 0%, #ffffff 100%) !important;

        :global {
            .ant-btn {
                color: #a1a4c8;
            }
        }

    }

    &:hover {
        .custom-card-actions {

            :global {
                .ant-btn {
                    color: #666;
                }
            }

        }
    }
}