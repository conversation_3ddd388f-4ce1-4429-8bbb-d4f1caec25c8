import req from '@/utils/request';

/**
 * 模版管理--新增
 * @param data
 * @returns
 */
export const create = (data: any): Promise<any> => req.post('/ai-manage/v1/template/add', data);
/**
 * 模版管理--编辑
 * @param data
 * @returns
 */
export const update = (data: any): Promise<any> => req.post('/ai-manage/v1/template/update', data);

/**
 * 模版管理--查询
 * @param data
 * @returns
 */
export const query = (data: any): Promise<any> => req.post('/ai-manage/v1/template/query', data);

/**
 * 模版管理--查询详情
 * @param data
 * @returns
 */
export const queryDetail = (params: any): Promise<any> => req.get('/ai-manage/v1/template/queryDetail', { params });

/**
 * 模版管理--删除
 * @param data
 * @returns
 */
export const deletePropmt = (params: any): Promise<any> => req.get('/ai-manage/v1/template/delete', { params });

/**
 * 提示词--删除
 * @param data
 * @returns
 */
export const deleteMyPropmt = (params: any): Promise<any> => req.get('/ai-manage/v1/prompt/history/delete', { params });

/**
 * 提示词专家
 * @param data
 * @returns
 */
export const propmt = (data: any): Promise<any> => req.post('/seres-ai-prompt-service/api/v1/chat/completions', data);
/**
 * 提示词专家 收藏
 * @param data
 * @returns
 */
export const completions = (data: any): Promise<any> => req.post('/ai-manage/v1/prompt/history/save', data);

/**
 * 我的提示词
 * @param data
 * @returns
 */

export const getPromptList = (data: any): Promise<any> => req.post('/ai-manage/v1/prompt/history/query', data);

/**
 * 提示词优化
 * @param data
 * @returns
 */

export const getPromptOptimizerAi = (data: any): Promise<any> =>
  req.post('/seres-ai-prompt-service/api/v1/prompt_optimizer_ai', data);
/**
 * 提示词优化单条详情
 * @param data
 * @returns
 */

export const getPromptOptimizerAiDetail = (id: any): Promise<any> =>
  req.get(`/ai-manage/v1/prompt/history/detail?id=${id}`);

/**
 * 提示词评估
 * @param data
 * @returns
 */

export const getPromptEvaluator = (data: any): Promise<any> =>
  req.post('/seres-ai-prompt-service/api/v1/prompt_evaluator', data);
/**
 * 提示词对比
 * @param data
 * @returns
 */

export const getPromptCompare = (data: any): Promise<any> =>
  req.post('/seres-ai-prompt-service/api/v1/prompt_evaluator/compare', data);
