{"extends": ["stylelint-config-standard", "stylelint-config-standard-less", "stylelint-config-rational-order"], "plugins": ["stylelint-order"], "rules": {"selector-class-pattern": ["^(?:[a-z][a-z0-9]*)(-[a-z0-9]+)*$|^[a-z][a-zA-Z0-9]+$", {"message": "类名可使用小驼峰或短横线格式"}], "no-descending-specificity": [true, {"ignore": ["selectors-within-list"], "severity": "warning"}], "no-empty-source": null, "block-no-empty": null}, "customSyntax": "postcss-less", "ignoreFiles": ["**/*.ts", "**/*.tsx", "**/node_modules/**", "**/.next/**"]}