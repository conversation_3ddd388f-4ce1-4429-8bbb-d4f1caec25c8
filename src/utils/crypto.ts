import CryptoJS from 'crypto-js';
//解密
export const DecryptByAES = (word: string, key: string, iv: string) => {
  try {
    const encryptedHexStr = CryptoJS.enc.Hex.parse(word);
    const keyHex = CryptoJS.enc.Utf8.parse(key ? key : '5566778899ABCDEF');
    const ivHex = CryptoJS.enc.Utf8.parse(iv ? iv : 'ABCDEF5566778899');
    const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    const decrypt = CryptoJS.AES.decrypt(srcs, keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
  } catch (error) {
    return '';
  }
};

// 加密
export function EncryptByAES(word: string, key: string, iv: string) {
  const keyHex = CryptoJS.enc.Utf8.parse(key ? key : '5566778899ABCDEF');
  const ivHex = CryptoJS.enc.Utf8.parse(iv ? iv : 'ABCDEF5566778899');
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, keyHex, {
    iv: ivHex,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString().toUpperCase();
}
