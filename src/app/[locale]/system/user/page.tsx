'use client';
import React, { useState, useRef } from 'react';
import { Card, Input, Space, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import Layout from '@/components/Layout';
import PublicTable from '@/components/PublicTable';

interface ListTemplate {
  id: number;
  userName: string;
  employeeNumber: string;
  email: string;
  rolesNameDescription: string;
  isOnJob: number;
}

const UserPage: React.FC = () => {
  const [text, setText] = useState<string>('');
  const [defaultQuery, setDefaultQuery] = useState({});
  const publicTableRef = useRef<{
    getTableData: () => void;
    resetCurrentAndPageSize: () => void;
  }>(null);

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
      ellipsis: true,
    },
    {
      title: '工号',
      dataIndex: 'employeeNumber',
      key: 'employeeNumber',
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '所属角色',
      dataIndex: 'rolesNameDescription',
      key: 'rolesNameDescription',
    },
    {
      title: '是否在职',
      dataIndex: 'isOnJob',
      key: 'isOnJob',
      render: (_, record) => (record.isOnJob ? '在职' : '离职'),
    },
  ];

  const handleSearch = () => {
    setDefaultQuery({ text });
  };

  //重置列表
  const resetSearch = () => {
    setText('');
    setDefaultQuery({});
  };

  return (
    <Layout curActive={`/system/user`}>
      <div>
        <Card variant="borderless">
          <div
            style={{
              marginBottom: 24,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Space>
              <div>关键字:</div>
              <Input
                value={text}
                placeholder="请输入用户名称 ｜ 工号 ｜ 邮箱"
                style={{ width: 300 }}
                onChange={(e) => setText(e.target.value)}
              />
            </Space>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={resetSearch}>重置</Button>
            </Space>
          </div>
          <PublicTable
            ref={publicTableRef}
            columns={columns}
            url={'/ai-manage/v1/user/pageList'}
            rowKey={'id'}
            defaultQuery={defaultQuery}
          />
        </Card>
      </div>
    </Layout>
  );
};

export default UserPage;
