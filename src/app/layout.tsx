import type { Metada<PERSON> } from 'next';
// import { Inter } from "next/font/google";
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { getTranslations } from 'next-intl/server';
import '@/styles/globals.css';
import { ProgressWrapper } from '@/components/ProgressWrapper';
// import '@/styles/variables.less';
type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

export const metadata: Metadata = {
  title: '智能体矩阵',
};
export default function RootLayout({ children, params: { locale } }: Readonly<Props>) {
  return (
    <html lang={locale}>
      <head></head>
      <body>
        <ProgressWrapper>
          <AntdRegistry>{children}</AntdRegistry>
        </ProgressWrapper>
      </body>
    </html>
  );
}
