# AiProChat 知识库多选功能 - 重构版

## 功能概述

AiProChat 组件现在采用更灵活的架构，通过 `senderFooter` prop 支持自定义 footer 内容，知识库选择器作为独立组件，由父组件管理状态和逻辑。

## 架构优势

### 1. 关注点分离
- AiProChat 专注于聊天功能
- KnowledgeSelector 专注于知识库选择
- 父组件管理业务逻辑

### 2. 更好的可扩展性
- 可以轻松添加其他 footer 内容
- 知识库逻辑完全由父组件控制
- 支持更复杂的业务场景

### 3. 更清晰的数据流
- 单向数据流
- 状态提升到父组件
- 更容易测试和维护

## 组件说明

### AiProChat
纯净的聊天组件，不包含知识库相关逻辑。

```tsx
export type AiProChatProps = {
  loading?: boolean;
  chats?: ChatMessage[];
  onChatsChange?: (value: ((prevState: ChatMessage[]) => ChatMessage[]) | ChatMessage[]) => void;
  style?: React.CSSProperties;
  appStyle?: React.CSSProperties;
  helloMessage?: string;
  botAvatar?: string;
  request: (messages: ChatMessage[]) => Promise<Response>;
  senderFooter?: React.ReactNode; // 自定义 footer 内容
};
```

### KnowledgeSelector
独立的知识库选择器组件。

```tsx
export interface KnowledgeSelectorProps {
  knowledgeBases: KnowledgeBase[];
  selectedKnowledgeBases: number[];
  onKnowledgeBasesChange: (selectedIds: number[]) => void;
  loading?: boolean;
  placeholder?: string;
  style?: React.CSSProperties;
}
```

## 使用方法

### 1. 基础用法 - 自定义知识库选择器

```tsx
import { AiProChat } from './components/AiProChat';
import KnowledgeSelector, { KnowledgeBase } from './components/KnowledgeSelector';

const ChatPage = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<number[]>([]);

  const handleRequest = async (messages: ChatMessage[]) => {
    // 在请求中使用 selectedKnowledgeBases
    const response = await fetch('/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        messages,
        knowledgeBaseIds: selectedKnowledgeBases
      })
    });
    return response;
  };

  return (
    <AiProChat
      request={handleRequest}
      senderFooter={
        <KnowledgeSelector
          knowledgeBases={knowledgeBases}
          selectedKnowledgeBases={selectedKnowledgeBases}
          onKnowledgeBasesChange={setSelectedKnowledgeBases}
        />
      }
    />
  );
};
```

### 2. 使用封装组件

```tsx
import ChatWithKnowledge from './components/ChatWithKnowledge';

const ChatPage = () => {
  return (
    <ChatWithKnowledge
      helloMessage="您好，我是智能助手"
      style={{ height: '100vh' }}
    />
  );
};
```

### 3. 自定义 Footer 内容

```tsx
const CustomFooter = () => (
  <div style={{ padding: '8px 0', borderTop: '1px solid #f0f0f0' }}>
    <KnowledgeSelector {...knowledgeProps} />
    <div style={{ marginTop: '8px' }}>
      <Button size="small">其他功能</Button>
    </div>
  </div>
);

<AiProChat
  request={handleRequest}
  senderFooter={<CustomFooter />}
/>
```

## Props 说明

### AiProChat 新增属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `knowledgeBases` | `KnowledgeBase[]` | `[]` | 可选的知识库列表 |
| `selectedKnowledgeBases` | `number[]` | `[]` | 当前选中的知识库ID列表 |
| `onKnowledgeBasesChange` | `(selectedIds: number[]) => void` | - | 知识库选择变化回调 |
| `showKnowledgeSelector` | `boolean` | `true` | 是否显示知识库选择器 |
| `request` | `(messages, selectedKnowledgeBaseIds?) => Promise<Response>` | - | 请求函数，现在支持知识库参数 |

### KnowledgeBase 类型

```tsx
type KnowledgeBase = {
  id: number;
  name: string;
  content?: string;
  documentNum?: string;
  createTime?: string;
};
```

## 实现细节

### 1. 组件结构
- 知识库选择器位于 Sender 的 footer 中
- 使用 Ant Design 的 Select 组件实现多选
- 自定义标签渲染，显示书本图标

### 2. 状态管理
- 支持受控模式（通过 props 传入状态）
- 支持非受控模式（内部管理状态）
- 自动保存到 localStorage

### 3. 性能优化
- 使用 useCallback 优化函数引用
- 使用 useMemo 优化渲染性能
- 避免不必要的重渲染

## 样式定制

知识库选择器的样式可以通过以下方式定制：

```css
/* 选择器容器 */
.knowledge-selector {
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
}

/* 标签样式 */
.knowledge-tag {
  margin: 2px;
  color: blue;
}
```

## 注意事项

1. **API 兼容性**: 确保后端 API 支持知识库参数
2. **性能考虑**: 大量知识库时考虑分页或搜索功能
3. **用户体验**: 提供清晰的选择状态反馈
4. **错误处理**: 处理知识库加载失败的情况

## 更新日志

### v1.0.0
- 添加知识库多选功能
- 支持状态持久化
- 优化性能和用户体验
- 完善类型定义和文档
