'use client';
import React, { useState, useEffect } from 'react';
import { Space, Card, Table, Button, message, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import AddOrEditModal from './components/addOrEditModal';
import type { TableColumnsType } from 'antd';
import { getTreeList, deleteMenu } from '@/services/menu/api';
import { ListDataType, UpdateMenuData } from '@/types/menu';
import SetPermission from './components/setPermission';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';
import styles from './page.module.less';

const MenuPage: React.FC = () => {
  const { userInfo } = useStore();
  const [data, setData] = useState<ListDataType[]>([]);
  //新增菜单
  const [open, setOpen] = useState<boolean>(false);
  const [mode, setMode] = useState<'add' | 'edit'>('add');
  const [initialValues, setInitialValues] = useState<UpdateMenuData>();
  const [treeData, setTreeData] = useState<ListDataType[]>([]);
  //设置权限
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);
  const [currentData, setCurrentData] = useState<ListDataType>();
  const columns: TableColumnsType<ListDataType> = [
    {
      title: '菜单名称',
      dataIndex: 'title',
      key: 'title',
      width: 90,
    },
    {
      title: '菜单编码',
      dataIndex: 'code',
      key: 'code',
      width: 140,
    },
    {
      title: 'path',
      dataIndex: 'path',
      width: 150,
      key: 'path',
    },
    {
      title: 'icon',
      dataIndex: 'icon',
      width: 160,
      key: 'icon',
    },
    {
      title: '排序',
      dataIndex: 'orderNo',
      width: 40,
      key: 'orderNo',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 80,
      ellipsis: true,
      key: 'remark',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          {roleJudgment(userInfo, 'MENU_EDIT') && <a onClick={() => handleEdit(record)}>编辑</a>}
          {roleJudgment(userInfo, 'MENU_SET') && (
            <a
              onClick={() => {
                setCurrentData(record);
                setPermissionModalOpen(true);
              }}
            >
              权限设置
            </a>
          )}

          {roleJudgment(userInfo, 'MENU_DELETE') && (!record.children || !!(record.children.length === 0)) && (
            <a style={{ color: '#ff4d4f' }} onClick={() => handleDelete(record)}>
              删除
            </a>
          )}
        </Space>
      ),
    },
  ];

  //根据当前key查找父级树
  const findPathByKey = (data: ListDataType[], targetKey: number) => {
    const dfs = (node: ListDataType, path: number[]): number[] | null => {
      const newPath = [...path, node.key];
      if (node.key === targetKey) {
        return newPath;
      }
      if (node.children) {
        for (const child of node.children) {
          const result = dfs(child, newPath);
          if (result) {
            return result;
          }
        }
      }
      return null;
    };
    for (const root of data) {
      const result = dfs(root, []);
      if (result) {
        return result;
      }
    }
    return [];
  };

  //添加菜单弹窗-关闭
  const onCancel = () => {
    setOpen(false);
  };

  //添加菜单弹窗-确定
  const onSuccess = () => {
    setOpen(false);
    getTableData();
  };

  //新增菜单
  const addMenu = () => {
    setOpen(true);
    setMode('add');
    setInitialValues(undefined); //初始化表单
  };

  //编辑菜单
  const handleEdit = (record: any) => {
    setOpen(true);
    setMode('edit');
    const updatedRecord: UpdateMenuData = {
      ...record,
      name: record.title,
      parentId: findPathByKey(treeData, record.parentId),
    };
    setInitialValues(updatedRecord);
  };

  //删除菜单
  const handleDelete = (record: ListDataType) => {
    Modal.confirm({
      title: '确认删除',
      content: `确认删除该菜单：${record.title}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        const { success, msg } = await deleteMenu(record.key);
        if (success) {
          message.success('删除成功');
          getTableData();
        } else {
          message.error(msg || '删除失败');
        }
      },
    });
  };

  const processMenuData = (data: ListDataType[], parentCode?: string, parentPath?: string): ListDataType[] => {
    return data.map((item) => {
      const currentCode = item.code;
      const currentPath = item.path;

      // 添加 parentCode 和 parentPath
      const updatedItem: ListDataType = {
        ...item,
        parentCode: parentCode,
        parentPath: parentPath,
      };

      // 如果存在子节点，则递归处理子树
      if (item.children && item.children.length > 0) {
        updatedItem.children = processMenuData(item.children, currentCode, currentPath);
      }
      return updatedItem;
    });
  };

  const getTableData = async () => {
    getTreeList().then(({ success, resp, msg }) => {
      if (success) {
        const temp = processMenuData(resp, 'ROOT_MENU', '/');
        setData(temp);
        const newData = [
          {
            title: '根菜单',
            key: 0,
            code: 'ROOT_MENU',
            path: '/',
            children: temp,
          },
        ];
        setTreeData(newData);
      } else {
        message.error(msg || '查询失败');
      }
    });
  };

  // 处理权限设置
  const handlePermissionOk = () => {
    // 处理权限数据
    setPermissionModalOpen(false);
    getTableData(); // 重新获取数据
  };

  //设置表格行的样式，children.length === 0
  const getRowClassName = (record: ListDataType, index: number) => {
    if (!record.children || !!(record.children.length === 0)) {
      return styles['hide-expanded-icon'];
    }
    return '';
  };

  useEffect(() => {
    getTableData();
  }, []);

  return (
    <Layout curActive={`/system/menu`}>
      <Card variant="borderless">
        <Space
          style={{
            marginBottom: 16,
          }}
        >
          {roleJudgment(userInfo, 'MENU_ADD') && (
            <Button type="primary" icon={<PlusOutlined />} onClick={addMenu}>
              新增
            </Button>
          )}
        </Space>
        <Table<ListDataType> columns={columns} dataSource={data} rowClassName={getRowClassName} />
      </Card>
      <AddOrEditModal
        open={open}
        onCancel={onCancel}
        onSuccess={onSuccess}
        initialValues={initialValues}
        treeData={treeData}
        mode={mode}
      />
      <SetPermission
        open={permissionModalOpen}
        onCancel={() => setPermissionModalOpen(false)}
        onSuccess={handlePermissionOk}
        currentData={currentData as ListDataType}
      />
    </Layout>
  );
};

export default MenuPage;
