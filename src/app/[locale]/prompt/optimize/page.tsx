'use client';
import Layout from '@/components/Layout';
import useStore from '@/store/useStore';
import { Button, Flex, Form, Input, message, Typography } from 'antd';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import StyleSheet from '../page.module.less';
const { Title } = Typography;
const { TextArea } = Input;

import { completions, getPromptOptimizerAi, getPromptOptimizerAiDetail } from '@/services/prompt/api';
function PromptPage() {
  const [state, setState] = useState({
    id: '',
    content: '',
    best_optimized_prompt: '',
    optimization_history: '',
  });
  const searchParams = useSearchParams();
  const id = searchParams.get('id'); // 获取 query 参数 id
  const [loading, setLoading] = useState(false);
  const { userInfo } = useStore();

  const handleCollection = async () => {
    if (state.best_optimized_prompt && state.optimization_history) {
      const { resp, success } = await completions({
        id: state.id,
        input: state.content,
        output: state.best_optimized_prompt,
      });
      if (success) {
        message.success('保存成功');
      } else {
        message.error('保存失败');
      }
    }
  };
  const handleGenerate = async () => {
    if (!state.content) {
      message.error('请填写提示词');
      return;
    }
    try {
      setLoading(true);
      const obj = {
        conversationId: 'conversationId', // 考虑从配置文件中读取
        messages: [
          {
            question: 'question',
            role: 'user', // 固定角色名，考虑常量化
            content: state.content,
            id: '4',
          },
        ],
        userId: userInfo.id.toString(),
        stream: false,
      };
      const result = await getPromptOptimizerAi(obj);
      if (result.success && result.resp.length > 0) {
        const { best_optimized_prompt, optimization_history, id } = result.resp[0]?.result;
        setState((pre) => ({
          ...pre,
          ...{
            id,
            best_optimized_prompt,
            optimization_history,
          },
        }));
      } else {
        message.error(result.message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (id) {
      getPromptOptimizerAiDetail(id).then((res) => {
        const { success, resp } = res;
        if (success) {
          setState((pre) => ({
            ...pre,
            content: resp[0]?.output,
          }));
        }
      });
    }
  }, [id]);
  return (
    <Layout curActive="/prompt/optimize">
      <Title level={2} style={{ marginBottom: 24 }}>
        提示词优化
      </Title>
      <Form layout="vertical">
        <Form.Item>
          <TextArea
            disabled={loading}
            value={state.content}
            rows={10}
            placeholder="请输入原始提示词"
            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
              setState((prev) => ({ ...prev, content: e.target.value }))
            }
          />
        </Form.Item>
        <Form.Item>
          <Flex align="flex-start" justify="space-between">
            <span></span>
            <Button type="primary" onClick={handleGenerate} loading={loading} disabled={!state.content}>
              优化
            </Button>
          </Flex>
        </Form.Item>
        <Form.Item>
          <div className={StyleSheet.panel}>
            <div className={StyleSheet.panelTitle}>
              <Title level={4}>优化结果</Title>
              <Button type="primary" onClick={handleCollection} disabled={!state.id}>
                保存
              </Button>
            </div>
            <div className={StyleSheet.panelContent}>
              <div className={StyleSheet.panelContentItem}>
                <TextArea
                  value={state.best_optimized_prompt}
                  rows={10}
                  placeholder="优化后提示词"
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
                    setState((prev) => ({ ...prev, best_optimized_prompt: e.target.value }))
                  }
                />
              </div>
              <div className={StyleSheet.panelFooter}>
                <div className={StyleSheet.panelTitle} style={{ flexDirection: 'column' }}>
                  <Title level={4}>推理结果</Title>
                  <TextArea value={state.optimization_history} rows={10} placeholder="推理结果" disabled />
                </div>
              </div>
            </div>
          </div>
        </Form.Item>
      </Form>
    </Layout>
  );
}

export default PromptPage;
