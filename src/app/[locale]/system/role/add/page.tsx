'use client';
import React, { useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, message } from 'antd';
import { createRole } from '@/services/role/api';
import Layout from '@/components/Layout';
import { useRouter } from '@bprogress/next/app';
import CommonForm from '../components/commonForm';

interface FormRef {
  checkedKeys: React.Key[];
  functionAuth: React.Key[];
}

const AddRole: React.FC = () => {
  const formRef = useRef<FormRef>(null);
  const [form] = Form.useForm();
  const router = useRouter();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      createRoleFn(values);
    } catch (error) {
      message.error('有必填项未填写，请检查');
    }
  };

  //创建角色
  const createRoleFn = async (values: any) => {
    if (formRef.current) {
      const { success, msg } = await createRole({
        ...values,
        permissionIds: [...formRef.current?.checkedKeys!, ...Object.values(formRef.current.functionAuth).flat()],
      });
      if (success) {
        message.success('创建成功');
        router.push('/system/role');
      } else {
        message.error(msg || '创建失败');
      }
    }
  };

  const handleCancel = () => {
    router.push('/system/role');
  };

  return (
    <Layout curActive="/system/role">
      <Card title="新增角色" variant="borderless">
        <Form form={form} layout="vertical">
          <CommonForm ref={formRef} />
          <Form.Item>
            <div style={{ textAlign: 'right', marginTop: 24 }}>
              <Button onClick={handleCancel} style={{ marginRight: 16 }}>
                取消
              </Button>
              <Button type="primary" onClick={handleSubmit}>
                确定
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </Layout>
  );
};

export default AddRole;
