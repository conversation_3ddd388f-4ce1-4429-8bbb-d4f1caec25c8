'use client';
import { useEffect, useState, ReactNode } from 'react';
import { Menu } from 'antd';
import type { GetProp, MenuProps } from 'antd';
import * as Icons from '@ant-design/icons';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
  modelList: MenuItem[];
  setSelectBrand: (value: string) => void;
}

const BrandListPage: React.FC<IProps> = ({ modelList, setSelectBrand }) => {
  const [newModelList, setNewModelList] = useState<MenuItem[]>([]);

  const handleSelect: MenuProps['onSelect'] = ({ key }) => {
    setSelectBrand(key);
  };

  // 将字符串图标转换为 ReactNode
  const getIconComponent = (iconData: string): ReactNode => {
    if (!iconData) return null;

    // 判断是否为 HTTP 链接
    if (iconData.startsWith('http://') || iconData.startsWith('https://')) {
      return <img src={iconData} alt="icon" style={{ width: 16, height: 16, objectFit: 'contain' }} />;
    }

    // 判断是否为 SVG 字符串
    if (iconData.includes('<svg') && iconData.includes('</svg>')) {
      return (
        <span
          dangerouslySetInnerHTML={{ __html: iconData }}
          style={{ display: 'inline-flex', width: 16, height: 16, alignItems: 'center', justifyContent: 'center' }}
        />
      );
    }

    // 尝试作为 Ant Design 图标名称处理
    const IconComponent = (Icons as any)[iconData];
    if (IconComponent) {
      return <IconComponent />;
    }

    // 如果都不匹配，返回默认图标
    return <Icons.QuestionCircleOutlined />;
  };

  useEffect(() => {
    const processedList = modelList.map((item: any) => {
      const processedItem = { ...item };

      // 处理图标
      if (processedItem.icon) {
        processedItem.icon = getIconComponent(processedItem.icon);
      }

      // 确保 key 使用品牌值进行筛选
      if (!processedItem.key && processedItem.brand) {
        processedItem.key = processedItem.brand;
      }

      return processedItem;
    });

    setNewModelList([{ key: 'all', label: '全部数据' }, ...processedList]);
  }, [modelList]);
  return (
    <div>
      <Menu defaultSelectedKeys={['all']} onSelect={handleSelect} mode="inline" items={newModelList} />
    </div>
  );
};

export default BrandListPage;
