'use client';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Layout from '@/components/LayoutAgent';
import { message, Typography } from 'antd';
import { useParams } from 'next/navigation';
import { SettingOutlined } from '@ant-design/icons';
import AgentConfig from './insideChat';
import Chat from './outsideChat';
import { agentDetail, getAgentApi } from '@/services/agent/api';
import { AgentOperateContextProvider } from '@/contexts/agentOperateContext';
import { VariableItem } from '@/types/agent/index';
import StyleSheet from './page.module.less';

// 动态导入组件，禁用 SSR
const InsideFlow = dynamic(() => import('./insideWorkFlow/index'), {
  ssr: false,
});

const OutsideFlow = dynamic(() => import('./outsideWorkFlow/index'), {
  ssr: false,
});

const { Title } = Typography;

const Agent: React.FC = () => {
  const { appId } = useParams();
  const [detail, setDetail] = useState<any>({});
  const [chatUrl, setChatUrl] = useState<string>('');
  const [variables, setVariables] = useState<VariableItem[]>([]);
  const [prompt, setPrompt] = useState<string>('');

  useEffect(() => {
    getDetail();
  }, []);

  //获取详情
  const getDetail = async () => {
    const res = await agentDetail(Number(appId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //agent 获取agent URL
  const getApi = async () => {
    const res = await getAgentApi(Number(appId));
    if (res.success) {
      setChatUrl(res.resp[0].url);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    if (detail.relevanceAgentInfo) {
      getApi();
    }
  }, [detail]);

  return (
    <Layout curActive={`/agent/${appId}/operate`} detailData={detail}>
      {detail.source === 'inter' && (
        <Title level={4} className={StyleSheet.title}>
          <SettingOutlined />
          智能体配置
        </Title>
      )}
      {detail.applyType === 'chat' ? (
        <div className={detail.source === 'inter' ? StyleSheet.container : StyleSheet['container-external']}>
          {detail.source === 'inter' && (
            <AgentOperateContextProvider
              contextValue={{
                initValues: detail,
                getDetail,
                getApi,
              }}
            >
              <AgentConfig setVariables={setVariables} variables={variables} prompt={prompt} setPrompt={setPrompt} />
            </AgentOperateContextProvider>
          )}
          <Chat chatUrl={chatUrl} variables={variables} prompt={prompt} />
        </div>
      ) : (
        <>{detail.source === 'inter' ? <InsideFlow /> : <OutsideFlow url={chatUrl} />}</>
      )}
    </Layout>
  );
};

export default Agent;
