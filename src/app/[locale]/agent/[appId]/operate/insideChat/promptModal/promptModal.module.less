.promptModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-header {
      margin-bottom: 16px;
      border-bottom: 1px solid #e6f7ff;
    }

    .ant-modal-body {
      padding: 16px 24px;
    }

    .ant-form-item-label>label {
      font-weight: 500;
      color: #1677ff;
    }
  }
}

.promptDescription {
  margin-bottom: 16px;
  color: #8c8c8c;
  font-size: 14px;

  p {
    margin: 0;
    line-height: 1.5;
  }
}

.variablesSection {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.variablesList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-end;
}

.variableTag {
  cursor: pointer;
  transition: all 0.3s;
  background-color: #e6f7ff;
  border-color: #bae7ff;
  color: #1677ff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    background-color: #bae7ff;
    border-color: #91d5ff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.promptTextarea {
  font-family: 'Courier New', Courier, monospace;
  line-height: 1.6;
  border: 1px solid #e6f7ff;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

  &:focus {
    border-color: #1677ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

.regenerateButton {
  margin-left: auto;
}

// // 全局样式覆盖
// :global {
//   .ant-tag-blue {
//     background-color: #e6f4ff;
//     border-color: #91caff;
//     color: #1677ff;

//     &:hover {
//       background-color: #d4edff;
//     }
//   }

//   .ant-tooltip {
//     max-width: 300px;
//   }
// }