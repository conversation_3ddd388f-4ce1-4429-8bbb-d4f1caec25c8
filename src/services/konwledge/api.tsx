import req from '@/utils/request';
import { CreateParams, UpdateParams, KonwListParams, KonwDetailParams } from '@/types/konwledge/index';

export const create = ({ name, content = '' }: CreateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/create', { name, content });

export const update = ({ id, name, content = '' }: UpdateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/update', { id, name, content });

export const del = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/know/del', { id });

export const getList = ({ params: { name }, current, pageSize }: KonwListParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/list', { params: { name }, current, pageSize });

/**
 * 检索测试
 * @param data
 * @returns
 */

export const getSearchTest = (data: any): Promise<any> => req.post('/ai-manage/v1/know/searchTest', data);
export const uploadFileReq = (formData: FormData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/document/uploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
export const download = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/document/download', { id });

export const getDocList = ({
  params: { name, knowId },
  current,
  pageSize,
}: KonwDetailParams): Promise<SERES_RESPONSE> =>
  req.post(`/ai-manage/v1/document/list`, {
    params: { name, knowId },
    current,
    pageSize,
  });

export const analysis = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/document/analysis', { id });

export const deleteDocument = (id: number): Promise<SERES_RESPONSE> => req.post(`/ai-manage/v1/document/del`, { id });
/**
 * 消息对话框
 */
/**
 * 知识库llm对话
 * @param data
 * @returns
 */

// export const fetchKnowledgeChat = (data: any): Promise<any> => req.post('/ragflow/knowledge_base/knowledge_chat', data);
/**
 * 知识库llm历史对话列表
 * @param data
 * @returns
 */

export const doGetManual = (data: any): Promise<any> => req.post('/ragflow/knowledge_base/chat_history', data);
/**
 * 知识库llm历史对话列表 侧边栏列表
 * @param data
 * @returns
 */

export const getConversationManualGet = (data: any): Promise<any> =>
  req.post('/ragflow/knowledge_base/chat_list', data);
/**
 * 知识库llm单个详情
 * @param data
 * @returns
 */

export const doGetBotInfo = (data: any): Promise<any> => req.post('/ragflow/knowledge_base/chat_history', data);
/**
 * 知识库llm删除单个
 * @param data
 * @returns
 */

export const doGetConverManualDelete = (data: any): Promise<any> => req.post('/ragflow/knowledge_base/chat_list', data);

/**
 * 知识库llm更新名称
 * @param data
 * @returns
 */

export const doGetConverManualUpdate = (data: any): Promise<any> => req.post('/ragflow/knowledge_base/chat_list', data);
