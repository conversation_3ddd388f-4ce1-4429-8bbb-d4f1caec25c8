'use client';
import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, Button, Select, Radio, message, Row, Col } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { ToolPluginUpdate } from '@/types/tool';
import { save, update } from '@/services/tool/api';
import UploadAvatar from '@/components/UploadAvatar';

const { TextArea } = Input;
const { Option } = Select;

interface AddModalProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  editData?: ToolPluginUpdate | null;
  isEdit?: boolean;
}

const AddPluginModal: React.FC<AddModalProps> = ({ open, onCancel, onSuccess, editData, isEdit = false }) => {
  const [form] = Form.useForm();
  const uploadRef = useRef<{
    imageUrl: string;
    setImageUrl: (url: string) => void;
  }>(null);
  const [loading, setLoading] = useState(false);

  // 当弹窗打开时，如果是编辑模式，填充表单数据
  useEffect(() => {
    if (open) {
      if (isEdit && editData) {
        form.setFieldsValue({
          ...editData,
          headers: editData.headers ? JSON.parse(editData.headers) : [],
        });
        uploadRef.current?.setImageUrl(editData.icon || '');
      } else {
        form.resetFields();
        // 设置默认值
        form.setFieldsValue({
          authType: '无需认证',
          position: 'headers',
          headers: [],
        });
      }
    }
  }, [open, isEdit, editData, form]);

  // 提交表单
  const handleOk = async () => {
    const values = await form.validateFields();
    setLoading(true);
    // 处理headers数据，过滤空值
    const headers = (values.headers || [])
      .filter((item: any) => item && item.label && item.value)
      .map((item: any) => ({
        label: item.label,
        value: item.value,
      }));

    const submitData = {
      ...values,
      headers,
      icon: uploadRef.current?.imageUrl, // 图标
    };

    const result = isEdit && editData ? await update({ ...submitData, id: editData.id }) : await save(submitData);

    if (result.errorCode === 0) {
      message.success(isEdit ? '编辑成功' : '新增成功');
      onSuccess();
      onCancel();
    } else {
      message.error(result.errorMessage || (isEdit ? '编辑失败' : '新增失败'));
    }
    setLoading(false);
  };

  return (
    <Modal
      title={isEdit ? '编辑插件' : '新增插件'}
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      width={700}
      okText="确定"
      cancelText="取消"
      confirmLoading={loading}
      styles={{
        body: {
          maxHeight: '75vh',
          overflowY: 'auto',
          paddingBottom: 15,
        },
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '16px',
        },
      }}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 19 }}
        style={{ marginTop: '24px' }}
      >
        {/* 图标上传 */}
        <Form.Item label="插件图标" name="icon">
          <UploadAvatar ref={uploadRef} />
        </Form.Item>

        {/* 插件名称 */}
        <Form.Item label="插件名称" name="name" rules={[{ required: true, message: '请输入插件名称' }]}>
          <Input placeholder="请输入插件名称" maxLength={30} showCount allowClear />
        </Form.Item>

        {/* 插件描述 */}
        <Form.Item label="插件描述" name="description" rules={[{ required: true, message: '请输入插件描述' }]}>
          <TextArea
            placeholder="请输入插件描述，详细说明插件的功能和用途..."
            rows={4}
            maxLength={500}
            showCount
            allowClear
          />
        </Form.Item>

        {/* 插件URL */}
        <Form.Item
          label="插件 URL"
          name="baseUrl"
          rules={[
            { required: true, message: '请输入插件 URL' },
            { type: 'url', message: '请输入有效的URL地址' },
          ]}
        >
          <Input placeholder="https://example.com/api" allowClear />
        </Form.Item>

        {/* Headers */}
        <Form.Item label="Headers">
          <Form.List name="headers">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} style={{ marginBottom: 8 }}>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, 'label']}
                        rules={[{ required: true, message: '请输入Headers Name' }]}
                      >
                        <Input placeholder="Headers Name" allowClear />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[{ required: true, message: '请输入Headers value' }]}
                      >
                        <Input placeholder="Headers value" allowClear />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Button type="text" icon={<MinusCircleOutlined />} onClick={() => remove(name)} danger />
                    </Col>
                  </Row>
                ))}
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  添加 headers
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>

        {/* 认证方式 */}
        <Form.Item label="认证方式" name="authType" rules={[{ required: true, message: '请选择认证方式' }]}>
          <Select placeholder="请选择认证方式">
            <Option value="none">无需认证</Option>
            <Option value="apiKey">Service token / API key</Option>
          </Select>
        </Form.Item>

        {/* 当选择Service token时显示额外字段 */}
        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.authType !== currentValues.authType}>
          {({ getFieldValue }) => {
            const authType = getFieldValue('authType');
            return authType === 'apiKey' ? (
              <>
                {/* 参数位置 */}
                <Form.Item label="参数位置" name="position" rules={[{ required: true, message: '请选择参数位置' }]}>
                  <Radio.Group>
                    <Radio value="headers">headers</Radio>
                    <Radio value="query">query</Radio>
                  </Radio.Group>
                </Form.Item>

                {/* tokenKey */}
                <Form.Item label="tokenKey" name="tokenKey" rules={[{ required: true, message: '请输入tokenKey' }]}>
                  <TextArea placeholder="请输入tokenKey" rows={3} maxLength={500} showCount allowClear />
                </Form.Item>

                {/* tokenValue */}
                <Form.Item
                  label="tokenValue"
                  name="tokenValue"
                  rules={[{ required: true, message: '请输入tokenValue' }]}
                >
                  <TextArea placeholder="请输入tokenValue" rows={3} maxLength={2000} showCount allowClear />
                </Form.Item>
              </>
            ) : null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddPluginModal;
