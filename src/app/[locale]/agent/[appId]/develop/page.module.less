.apiCard {
    max-width: 600px;
    margin-top: 24px;
    background: #232326 !important;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 8%);

    .apiCardBody {
        padding: 0;
    }

    .apiCardHeader {
        padding: 16px 24px;
        color: #fff;
        font-weight: 600;
        font-size: 16px;
        border-bottom: 1px solid #353539;
    }

    .apiCardContent {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px;
        background: #19191b;
        border-radius: 0 0 20px 20px;
    }

    .apiUrl {
        color: #fff;
        font-size: 16px;
    }

    .copyBtn {
        color: #b1b1b3;
        font-weight: 500;
        background: #232326;
        border: none;
        border-radius: 16px;

        &:hover {
            color: #fff !important;
            background: #232326 !important;
        }
    }
}