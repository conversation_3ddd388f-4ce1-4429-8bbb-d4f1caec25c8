import React from 'react';
import { Card, Tooltip } from 'antd';
import { InfoCircleOutlined, LineChartOutlined } from '@ant-design/icons';
import ReactEcharts from 'echarts-for-react';
import styles from './index.module.less';

interface DataPoint {
  xaxis: string[];
  yaxis: string[];
}

interface StatisticsChartProps {
  title: string;
  data: DataPoint;
  period: string;
  tooltip?: string;
  color: string;
}

const StatisticsChart: React.FC<StatisticsChartProps> = ({ title, data, period, tooltip, color }) => {
  const getOtion = () => {
    const option = {
      grid: {
        top: '30px',
        bottom: 16,
        right: '25px',
        left: '25px',
        containLabel: true,
      },
      // legend: {
      //   right: '20px',
      //   top: '20px',
      //   itemHeight: 10,
      //   itemWidth: 10,
      // },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        // name: '时段',
        nameTextStyle: {
          color: 'rgba(0,0,0,0.45)',
        },
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(0,0,0,0.45)',
          },
        },
        axisLabel: {
          show: true,
        },
        data: data ? data.xaxis : [],
      },
      yAxis: [
        {
          show: true,
          nameTextStyle: {
            color,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color,
            },
          },
          axisLabel: {
            show: true,
          },
          // splitLine: { show: false },
        },
      ],
      series: [
        {
          name: title,
          data: data ? data.yaxis : [],
          type: 'line',
          smooth: true,
          itemStyle: {
            color,
          },
        },
      ],
    };
    return option;
  };

  return (
    <Card className={styles.statisticsCard} bordered={false} style={{ borderRadius: 4, border: '1px solid #e9e9e9' }}>
      <div className={styles.titleRow}>
        <div className={styles.titleWrapper}>
          <div className={styles.title}>
            <div
              style={{
                width: 25,
                height: 25,
                background: color,
                borderRadius: '50%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <LineChartOutlined style={{ color: '#fff', fontWeight: 'bold', fontSize: 14 }} />
            </div>

            {/* <Image src={src} width={25} height={25}></Image> */}
            <span> {title}</span>
          </div>
          {tooltip && (
            <Tooltip title={tooltip}>
              <InfoCircleOutlined className={styles.infoIcon} />
            </Tooltip>
          )}
        </div>
        <span className={styles.period}>{period}</span>
      </div>
      <div style={{ position: 'relative', height: 200, width: '100%' }}>
        <ReactEcharts
          option={getOtion()}
          style={{ width: '100%', height: 200, position: 'absolute', top: 0 }}
          className="react_for_echarts"
        />
      </div>
    </Card>
  );
};

export default StatisticsChart;
