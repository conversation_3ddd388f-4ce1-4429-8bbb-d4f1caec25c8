import { createContext, useContext, Dispatch, SetStateAction } from 'react';

// 定义上下文类型
interface InjectMessageType {
  setPopOpen: Dispatch<SetStateAction<boolean>>;
  setInitialValues: Dispatch<SetStateAction<{}>>;
  setMode: Dispatch<SetStateAction<'add' | 'edit'>>;
  getList: () => void;
}

// 创建上下文对象，使用更具描述性的名称
const AgentListContext = createContext<InjectMessageType | null>(null);

// 创建一个提供上下文值的组件
export const AgentListContextProvider = ({
  children,
  contextValue,
}: {
  children: React.ReactNode;
  contextValue: InjectMessageType;
}) => {
  return <AgentListContext.Provider value={contextValue}>{children}</AgentListContext.Provider>;
};

// 创建一个钩子来使用上下文
export const useAgentListContext = () => {
  const context = useContext(AgentListContext);
  if (!context) {
    throw new Error('useAgentListContext must be used within a AgentListContextProvider');
  }
  return context;
};
