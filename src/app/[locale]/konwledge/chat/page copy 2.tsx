'use client';
// 导入必要的依赖
import { useState, CSSProperties, useEffect } from 'react';
import { Button, Divider, Flex, Switch, theme } from 'antd';
import { ApiOutlined, LinkOutlined, SearchOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import { Bubble, Sender } from '@ant-design/x';
import useStore from '@/store/useStore';
import styles from './page.module.less';
import ChatComponent from './components/ChatComponent';
export const outerStyle: CSSProperties = {
  margin: 0,
  padding: 0,
  height: 'calc(100vh - 70px)',
};

const Chat: React.FC = () => {
  const { token } = theme.useToken();
  const [loading, setLoading] = useState<boolean>(false);
  const [value, setValue] = useState<string>('');

  const iconStyle = {
    fontSize: 18,
    color: token.colorText,
  };

  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        setLoading(false);
        setValue('');
        console.log('Send message successfully!');
      }, 2000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [loading]);

  return (
    <Layout curActive="/konwledge/chat" outerStyle={outerStyle}>
      <Bubble content="Hello world!" />
      <Sender
        value={value}
        onChange={setValue}
        autoSize={{ minRows: 2, maxRows: 6 }}
        placeholder="Press Enter to send message"
        footer={({ components }) => {
          const { SendButton, LoadingButton, SpeechButton } = components;
          return (
            <Flex justify="space-between" align="center">
              <Flex gap="small" align="center">
                <Button style={iconStyle} type="text" icon={<LinkOutlined />} />
                <Divider type="vertical" />
                Deep Thinking
                <Switch size="small" />
                <Divider type="vertical" />
                <Button icon={<SearchOutlined />}>Global Search</Button>
              </Flex>
              <Flex align="center">
                <Button type="text" style={iconStyle} icon={<ApiOutlined />} />
                <Divider type="vertical" />
                <SpeechButton style={iconStyle} />
                <Divider type="vertical" />
                {loading ? <LoadingButton type="default" /> : <SendButton type="primary" disabled={false} />}
              </Flex>
            </Flex>
          );
        }}
        onSubmit={() => {
          setLoading(true);
        }}
        onCancel={() => {
          setLoading(false);
        }}
        actions={false}
      />
    </Layout>
  );
};

export default Chat;
