'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message, Cascader, InputNumber, Row, Col } from 'antd';
import { addMenu, editMenu } from '@/services/menu/api';
import { ListDataType, CreateMenuData, UpdateMenuData } from '@/types/menu';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<UpdateMenuData>;
  mode?: 'add' | 'edit';
  treeData: ListDataType[];
}

const AddOrEditModal: React.FC<IProps> = ({ open, onCancel, onSuccess, initialValues, mode = 'add', treeData }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  //父级菜单选择
  const onCascaderChange = (value: number[], selectedOptions: ListDataType[]) => {
    const selectLastOption = selectedOptions.slice(-1)[0];
    form.setFieldsValue({
      parentCode: selectLastOption.code,
      parentPath: selectLastOption.path,
    });
  };

  const handleSubmit = async () => {
    form.validateFields().then(async (values) => {
      setSubmitLoading(true);
      if (mode === 'add') {
        createHandle(values);
      } else {
        // 编辑模式
        updateHandle({ id: initialValues?.key, ...values });
      }
    });
  };

  const createHandle = (data: CreateMenuData) => {
    addMenu({ ...data, parent: data.parentId?.at(-1)! }).then((res) => {
      if (res.success) {
        message.success('新增成功');
        onSuccess();
      } else {
        message.error(res.msg || '新增失败');
      }
      setSubmitLoading(false);
    });
  };

  const updateHandle = (data: UpdateMenuData) => {
    editMenu({ ...data, parent: data.parentId?.at(-1)! }).then((res) => {
      if (res.success) {
        message.success('更新成功');
        onSuccess();
      } else {
        message.error(res.msg || '更新失败');
      }
      setSubmitLoading(false);
    });
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue({
        ...initialValues,
      });
    }
  }, [open, initialValues]);

  useEffect(() => {
    if (!open) {
      form.resetFields();
    }
  }, [open]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title={mode == 'add' ? '新增菜单' : '编辑菜单'}
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={1000}
      destroyOnClose
    >
      <Form form={form} {...formItemLayout}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="父级菜单名称" name="parentId" rules={[{ required: true, message: '请选择父级菜单名称' }]}>
              <Cascader
                options={treeData}
                changeOnSelect
                placeholder="请选择"
                fieldNames={{
                  label: 'title',
                  value: 'key',
                  children: 'children',
                }}
                style={{ width: '100%' }}
                onChange={onCascaderChange}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="父级菜单编码" name="parentCode">
              <Input placeholder="父级菜单编码" disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="父级菜单Path" name="parentPath">
              <Input placeholder="父级菜单Path" disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="菜单名称" name="name" rules={[{ required: true, message: '请输入菜单名称' }]}>
              <Input placeholder="请输入菜单名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="菜单编码" name="code" rules={[{ required: true, message: '请输入菜单编码' }]}>
              <Input placeholder="请输入菜单编码" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Path" name="path" rules={[{ required: true, message: '请输入Path' }]}>
              <Input placeholder="请输入Path" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Icon" name="icon">
              <Input placeholder="请输入Icon" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="排序" name="orderNo" rules={[{ required: true, message: '请输入排序' }]}>
              <InputNumber min={1} placeholder="请输入排序" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="描述" name="remark" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
              <TextArea placeholder="请输入描述" rows={6} maxLength={200} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddOrEditModal;
