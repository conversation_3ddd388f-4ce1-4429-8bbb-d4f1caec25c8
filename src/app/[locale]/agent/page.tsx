'use client';
import { useEffect, useState, useCallback } from 'react';
import { message, Button, Empty, Input, Space, Checkbox } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import debounce from 'lodash-es/debounce';
import Layout from '@/components/Layout';
import CardItem from './components/card/cardItem';
import { CardData } from '@/types/agent/index';
import AddOrEditAgent from './components/addOrEditAgent';
import { getAgentList } from '@/services/agent/api';
import { AgentListContextProvider } from '@/contexts/agentListContext';
import StyleSheet from './page.module.less';

const pageSize = 50;

export default function Agent() {
  const [popOpen, setPopOpen] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState({});
  const [mode, setMode] = useState<'add' | 'edit'>('add');
  const [agentList, setAgentList] = useState<CardData[]>([]);
  const [loading, setLoading] = useState(false);
  const [current, setCurrent] = useState<number>(1);
  const [hasMore, setHasMore] = useState(true); // 是否还有更多数据
  const [search, setSearch] = useState<string>(''); //搜索框内容
  const [isMine, setIsMine] = useState<boolean>(false); //是否我创建的
  const onCancel = () => {
    setPopOpen(false);
    setMode('add');
  };
  const onSuccess = () => {
    onCancel();
    getList();
  };

  const getList = async () => {
    if (loading) return;
    setLoading(true);
    setCurrent(1);
    setHasMore(true);

    const { success, resp, msg } = await getAgentList({
      current: 1,
      pageSize: pageSize,
      params: { search, isMine },
    });

    if (success) {
      setAgentList(resp[0].list);
      if (resp[0].pagination.total <= resp[0].list.length) {
        setHasMore(false);
      }
    } else {
      message.error(msg || '查询失败');
    }
    setLoading(false);
  };

  const handleScroll = async () => {
    if (loading || !hasMore) return;

    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = document.documentElement.scrollTop;
    const clientHeight = document.documentElement.clientHeight;

    if (scrollHeight - scrollTop - clientHeight < 100) {
      setLoading(true);
      const nextPage = current + 1;

      const { success, resp, msg } = await getAgentList({
        current: nextPage,
        pageSize: pageSize,
        params: { search, isMine },
      });

      if (success) {
        setAgentList((prev) => [...prev, ...resp[0].list]);
        setCurrent(nextPage);
        if (resp[0].pagination.total <= agentList.length + resp[0].list.length) {
          setHasMore(false);
        }
      } else {
        message.error(msg || '加载更多失败');
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loading, hasMore, current, search, isMine]);

  useEffect(() => {
    getList();
  }, [search, isMine]);

  const checkChange = (value: boolean) => {
    setIsMine(value);
  };

  const handleChange = useCallback(
    debounce((value: string) => {
      setSearch(value);
    }, 500),
    [],
  );

  return (
    <Layout curActive="/agent">
      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 20 }}>
        <Input
          style={{ width: 600 }}
          size="large"
          placeholder="搜索"
          suffix={<SearchOutlined />}
          onChange={(e) => handleChange(e.target.value)}
          allowClear
        />
      </div>
      <div className={StyleSheet.pageHeader}>
        <Space size="middle">
          <div>
            我创建的&nbsp;<Checkbox onChange={(e) => checkChange(e.target.checked)}></Checkbox>
          </div>
          <Button
            type="primary"
            onClick={() => setPopOpen(true)}
            style={{
              background: 'linear-gradient(90deg, #2080ff, #6a5af9)',
              border: 'none',
              boxShadow: '0 4px 12px rgba(32, 128, 255, 0.3)',
            }}
          >
            创建智能体
          </Button>
        </Space>
      </div>

      <AgentListContextProvider
        contextValue={{
          setPopOpen,
          setInitialValues,
          setMode,
          getList,
        }}
      >
        <div className={StyleSheet.container}>
          {!agentList.length ? (
            <div className={StyleSheet.emptyContainer}>
              <Empty description="暂无智能体数据" image={Empty.PRESENTED_IMAGE_DEFAULT} />
            </div>
          ) : (
            agentList?.map((item, index) => {
              return <CardItem key={index} data={item} />;
            })
          )}
        </div>
      </AgentListContextProvider>
      {!hasMore && <div className={StyleSheet.noMoreData}>没有更多数据了～</div>}

      <AddOrEditAgent
        open={popOpen}
        initialValues={initialValues}
        mode={mode}
        onCancel={onCancel}
        onSuccess={onSuccess}
      />
    </Layout>
  );
}
