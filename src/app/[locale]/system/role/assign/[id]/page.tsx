// ✅ 修复后：将 'use client' 放在文件最顶部
'use client';

import React, { useRef, useState, useEffect } from 'react';
import { Card, Input, Button, message } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import PublicTable from '@/components/PublicTable';
import { useRouter, useParams } from 'next/navigation';
import type { ColumnsType } from 'antd/es/table';
import { getRoleInfo, assignUser } from '@/services/role/api';
import styles from './page.module.less';

const AssignRole: React.FC = () => {
  const router = useRouter();
  const { id } = useParams();
  const [keyword, setKeyword] = useState<string>('');
  const [defaultQuery, setDefaultQuery] = useState({});
  const [roleName, setRoleName] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]); // 已选中的用户
  const [selectedUserKeys, setSelectedUserKeys] = useState<number[]>([]); // 已选中的用户
  const tableRef = useRef<{
    resetCurrentAndPageSize: () => void;
  }>(null);

  // 获取角色信息
  const getRoleInfoFn = async () => {
    const { success, resp, msg } = await getRoleInfo(Number(id));
    if (success) {
      const { linkUsers } = resp[0];
      linkUsers.map((item: any) => (item.id = item.userId));
      setSelectedUsers(linkUsers);
      setSelectedUserKeys(linkUsers.map((item: any) => item.id));
      setRoleName(resp[0].roleName);
    } else {
      message.error(msg || '获取角色信息失败');
    }
  };

  useEffect(() => {
    getRoleInfoFn();
  }, []);

  const columns: ColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
  ];

  // 搜索
  const handleSearch = () => {
    setDefaultQuery({ text: keyword });
  };

  // 重置
  const handleReset = () => {
    setKeyword('');
    setDefaultQuery({});
    if (tableRef.current) {
      tableRef.current.resetCurrentAndPageSize();
    }
  };

  const handleSelect = (record, selected) => {
    setSelectedUserKeys((prevKeys) => {
      const newKeys = selected ? [...prevKeys, record.id] : prevKeys.filter((id) => id !== record.id);
      return Array.from(new Set(newKeys));
    });
    setSelectedUsers((prevUsers) => {
      const newUsers = selected ? [...prevUsers, record] : prevUsers.filter((user) => user.id !== record.id);
      // 去重，确保每个 id 只出现一次
      const map = new Map();
      newUsers.forEach((item) => map.set(item.id, item));
      return Array.from(map.values());
    });
  };

  const handleSelectAll = (selected, selectedRows, changeRows) => {
    setSelectedUserKeys((prevKeys) => {
      const changeKeys = changeRows.map((item) => item.id);
      const newKeys = selected
        ? Array.from(new Set([...prevKeys, ...changeKeys]))
        : prevKeys.filter((id) => !changeKeys.includes(id));
      return newKeys;
    });
    setSelectedUsers((prevUsers) => {
      const newUsers = selected
        ? [...prevUsers, ...changeRows]
        : prevUsers.filter((user) => !changeRows.some((row) => row.id === user.id));
      // 去重
      const map = new Map();
      newUsers.forEach((item) => map.set(item.id, item));
      return Array.from(map.values());
    });
  };

  // 处理选择变化
  const handleSelectionChange = (selectedRowKeys: number[], selectedRows: any[]) => {
    setSelectedUsers(selectedRows);
    setSelectedUserKeys(selectedRowKeys);
  };

  // 单个删除
  const handleDeleteUser = async (record: any) => {
    setSelectedUsers((prevUsers) => prevUsers.filter((user) => user.id !== record.id));
    setSelectedUserKeys((prevKeys) => prevKeys.filter((key) => key !== record.id));
  };

  // 全部清空
  const handleClear = () => {
    setSelectedUsers([]);
    setSelectedUserKeys([]);
  };

  // 保存分配
  const handleSave = async () => {
    if (!selectedUserKeys.length) {
      message.warning('请选择要分配的用户');
      return;
    }
    const { success, msg } = await assignUser({
      roleId: Number(id),
      userIds: selectedUserKeys,
    });
    if (success) {
      message.success('分配成功');
      router.push('/system/role');
    } else {
      message.error(msg || '分配失败');
    }
  };

  return (
    <Layout curActive="/system/role">
      <Card
        className={styles.card}
        title={`分配用户 - ${roleName}`}
        variant="borderless"
        extra={
          <div className={styles['btn-group']}>
            <Button onClick={() => router.push('/system/role')} style={{ marginRight: 16 }}>
              取消
            </Button>
            <Button type="primary" onClick={handleSave}>
              确定
            </Button>
          </div>
        }
      >
        <div style={{ display: 'flex', gap: '24px' }}>
          <div style={{ flex: 1 }}>
            <div style={{ marginBottom: 16 }}>
              <Input
                placeholder="请输入姓名或邮箱"
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: 200, marginRight: 8 }}
                onPressEnter={handleSearch}
              />
              <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
                查询
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </div>
            <PublicTable
              ref={tableRef}
              columns={columns}
              url="/ai-manage/v1/user/pageList"
              rowKey="id"
              rowSelection={{
                selectedRowKeys: selectedUserKeys,
                selectedRows: selectedUsers,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
              }}
              defaultQuery={defaultQuery}
            />
          </div>
          <div
            style={{
              width: 400,
              paddingTop: 12,
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>已分派账号（{selectedUsers.length}）</span>
              <span style={{ cursor: 'pointer' }} onClick={handleClear}>
                清空
              </span>
            </div>
            <div
              style={{
                border: '1px solid #ccc',
                marginTop: 16,
                padding: 6,
                height: 480,
                overflowY: 'auto',
              }}
            >
              {selectedUsers.map((user: any) => (
                <div key={user.id} className={styles['user-item']}>
                  <span>{user.userName}</span>
                  <span className={styles['user-item-delete']} onClick={() => handleDeleteUser(user)}>
                    <DeleteOutlined />
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </Layout>
  );
};

export default AssignRole;
