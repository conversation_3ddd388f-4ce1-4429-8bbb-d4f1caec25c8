.container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 24px;
    min-height: calc(100vh - 250px);
}

/* 添加 Empty 组件的样式 */
.emptyContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    grid-column: 1 / -1;
    /* 占据所有列 */
}

.pageHeader {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, rgba(0, 0, 0, 0), rgba(32, 128, 255, 0.8), rgba(0, 0, 0, 0));
    }
}

.noMoreData {
    width: 100%;
    text-align: center;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
    position: relative;

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 80px;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(153, 153, 153, 0.5));
    }

    &::before {
        right: 60%;
    }

    &::after {
        left: 60%;
        transform: rotate(180deg);
    }
}