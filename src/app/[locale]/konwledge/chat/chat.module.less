.mixin {
    font-size: 1.2rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    color: #42484A;
    cursor: pointer;

    &:hover {
        background-color: #EDF0F1;
    }
}

.layout {
    position: relative;
    width: 100%;
    min-width: 1000px;
    height: 100%;
    display: flex;
    background: var(--ant-color-bg-container);
    font-family: AlibabaPuHuiTi, var(--ant-font-family), sans-serif;

    .isNotOpen {
        position: absolute;
        left: 20px;
        top: 20px;
        z-index: 100;
        .mixin();
    }

    .sider:not(.is-open) {
        width: 0;
        opacity: 0;
        flex: 0 0 0;
    }

    .sider.is-open {
        overflow: hidden;
        /* 确保内容不溢出 */
        white-space: nowrap;
        /* 防止文本换行 */
        flex: 1 1 auto;
        /* 当侧边栏打开时，占据可用空间 */
    }

    .sider {
        background: var(--ant-color-bg-layout);
        width: 280px;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 0 12px;
        box-sizing: border-box;

        .logo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            margin: 24px 0;
            // border-bottom: 1px solid var(--main-color);
            span {
                font-weight: bold;
                color: var(--ant-color-text);
                font-size: 16px;
            }

            .close {
                /* stylelint-disable-next-line CssSyntaxError */
                .mixin();
            };
        }

        .addBtn {
            background: #1677ff0f;
            border: 1px solid #1677ff34;
            height: 40px;
        }

        .conversations {
            flex: 1;
            overflow-y: auto;
            margin-top: 12px;
            padding: 0;

            .ant-conversations-list {
                padding-inline-start: 0;
            }
        }
    }

    .chat {
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding-block: var(--ant-padding-lg);
        gap: 16px;

        .chatList {
            flex: 1;
            overflow: auto;

            .loadingMessage {
                background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
                background-size: 100% 2px;
                background-repeat: no-repeat;
                background-position: bottom;
            }

            .placeholder {
                padding-top: 32px;

                .chatPrompt {
                    .ant-prompts-label {
                        color: #000000e0 !important;
                    }

                    .ant-prompts-desc {
                        color: #000000a6 !important;
                        width: 100%;
                    }

                    .ant-prompts-icon {
                        color: #000000a6 !important;
                    }
                }
            }
        }
    }

    .sender {
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
    }
}