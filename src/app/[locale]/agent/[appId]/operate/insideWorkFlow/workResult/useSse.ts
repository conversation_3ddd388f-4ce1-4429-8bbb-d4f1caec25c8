import { events } from 'fetch-event-stream';
import { useState } from 'react';
import Cookies from 'js-cookie';

type StartParams = {
  data: any;
  onMessage: (message: string) => void;
  onError?: (err?: Error) => void;
  onFinished: () => void;
};

const baseUrl = `${process.env.NEXT_PUBLIC_AI_BASE_URL}/`;

const isBrowser: boolean = typeof window !== 'undefined';

export const useSse = (url: string, headers?: any, options?: any) => {
  const ctrl = new AbortController();
  const [loading, setLoading] = useState(false);

  let sseUrl = url;
  if (sseUrl.startsWith('/')) {
    sseUrl = baseUrl + sseUrl.substring(1);
  }

  const token = isBrowser ? Cookies.get('access_token') || '' : null;

  const sseHeaders = {
    Authorization: token || '',
    ...headers,
  };

  return {
    loading: loading,
    stop: () => {
      ctrl.abort('by stop() invoked!');
      setLoading(false);
    },
    start: async (params: StartParams) => {
      try {
        setLoading(true);
        let res = await fetch(sseUrl, {
          method: 'post',
          signal: ctrl.signal,
          headers: sseHeaders,
          body: JSON.stringify(params.data),
        });

        if (!res.ok) {
          params.onError?.();
          return;
        }
        try {
          let msgEvents = events(res, ctrl.signal);
          for await (let event of msgEvents) {
            if (event.data && '[DONE]' !== event.data.trim()) {
              if (options === 'ollamaInstall') {
                console.log('ollamaInstall', event.data);
                params.onMessage(event.data);
              } else {
                console.log('ollamaInstall2', event.data);
                let temp = JSON.parse(event.data);
                params.onMessage(temp.content);
              }
            }
          }
        } catch (err) {
          console.error('error', err);
          params.onError?.();
        } finally {
          params.onFinished();
        }
      } finally {
        setLoading(false);
      }
    },
  };
};
