'use client';
import React, { useState, useRef, useEffect } from 'react';
import { Card, Space, message, Drawer, Avatar, Typography, Input, Button, Form, DatePicker } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { RobotOutlined, UserOutlined } from '@ant-design/icons';
import Layout from '@/components/LayoutAgent';
import PublicTable from '@/components/PublicTable';
import { useParams } from 'next/navigation';
import { getAgentMessage, agentDetail } from '@/services/agent/api';
import styles from './page.module.less';

const { Text } = Typography;
const { RangePicker } = DatePicker;
interface ListTemplate {
  title: string;
  conversationId: string;
  userName: string;
  msgNum: number;
  createTime: string;
  updateTime: string;
}

interface ChatMessage {
  conversationId: string;
  messageId: string;
  parentMessageId: string;
  agentCode: string;
  query: string;
  answer: string;
}

interface SearchParams {
  search: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
}

const LogPage: React.FC = () => {
  const [form] = Form.useForm();
  const agentId = useParams().appId;
  const [open, setOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<ListTemplate | null>(null);
  const [current, setCurrent] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [defaultQuery, setDefaultQuery] = useState<SearchParams>({
    search: undefined,
    startTime: undefined,
    endTime: undefined,
  });
  const [detail, setDetail] = useState<any>({});

  const publicTableRef = useRef<{
    getTableData: () => void;
    selectedRowKeys: any[];
    setSelectedRowKeys: (keys: any[]) => void;
  }>(null);

  const showDrawer = (record: ListTemplate) => {
    setCurrentRecord(record);
    setOpen(true);
    getLogMessage(record?.conversationId, current);
  };

  //加载更多
  const loadMore = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    await getLogMessage(currentRecord?.conversationId, current);
    setLoading(false);
  };

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      loadMore();
    }
  };

  const getLogMessage = async (conversationId, current) => {
    const { success, resp, msg } = await getAgentMessage({ current, pageSize: 10, conversationId });
    if (success) {
      setChatMessages((prev) => [...prev, ...resp[0].list]);
      setCurrent((c) => c + 1);
      if (resp[0].pagination.total <= chatMessages.length + resp[0].list.length) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    } else {
      message.error(msg || '获取日志失败');
    }
  };
  const onClose = () => {
    setOpen(false);
    //重置弹窗基础数据
    setChatMessages([]);
    setCurrent(1);
  };

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      width: 200,
    },
    {
      title: '用户或终端',
      dataIndex: 'userName',
      key: 'userName',
      ellipsis: true,
      width: 200,
    },
    {
      title: '消息数',
      dataIndex: 'msgNum',
      key: 'msgNum',
      width: 60,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => showDrawer(record)}>查看日志</a>
        </Space>
      ),
    },
  ];

  const handleSearch = () => {
    form.validateFields().then(async (values) => {
      if (values.time) {
        const [start, end] = values.time;
        values.startTime = start.format('YYYY-MM-DD HH:mm');
        values.endTime = end.format('YYYY-MM-DD HH:mm');
      }
      delete values.time;
      setDefaultQuery(values); // 设置默认查询参数
    });
  };

  const resetSearch = () => {
    form.resetFields();
    setDefaultQuery({
      search: undefined,
      startTime: undefined,
      endTime: undefined,
    }); // 重置查询参数
  };

  //获取智能体详情
  const getDetail = async () => {
    const res = await agentDetail(Number(agentId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    getDetail();
  }, []);

  return (
    <Layout curActive={`/agent/${agentId}/log`} detailData={detail}>
      <Card variant="borderless">
        <Form form={form} layout="inline" style={{ marginBottom: 24 }}>
          <Form.Item label="关键字" name="search">
            <Input placeholder="输入查询关键字" allowClear />
          </Form.Item>
          <Form.Item name="time" label="时间">
            <RangePicker allowClear />
          </Form.Item>
          <Form.Item label={null}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={resetSearch}>重置</Button>
            </Space>
          </Form.Item>
        </Form>
        <PublicTable
          ref={publicTableRef}
          columns={columns}
          url={'/ai-manage/v1/agent/conversation/log'}
          rowKey={'id'}
          defaultQuery={defaultQuery}
        />
        <Drawer title={currentRecord?.title || '对话日志'} width={600} closable={true} onClose={onClose} open={open}>
          <div className={styles.chatContainer} onScroll={handleScroll}>
            <div className={styles.chatMessages}>
              {chatMessages.map(({ messageId, query, answer }) => [
                <div key={messageId + '-user'} className={`${styles.messageItem} ${styles.userMessage}`}>
                  <div className={styles.messageAvatar}>
                    <div className={styles.userAvatarWrapper}>
                      <Avatar icon={<UserOutlined />} className={styles.userAvatar} size={40} />
                      {/* <div className={styles.userName}>你</div> */}
                    </div>
                  </div>
                  <div className={styles.messageContent}>
                    <div className={styles.messageBubble}>
                      <Text>{query}</Text>
                    </div>
                  </div>
                </div>,
                <div key={messageId + '-assistant'} className={`${styles.messageItem} ${styles.assistantMessage}`}>
                  <div className={styles.messageAvatar}>
                    <Avatar icon={<RobotOutlined />} className={styles.assistantAvatar} size={40} />
                  </div>
                  <div className={styles.messageContent}>
                    <div className={styles.messageBubble}>
                      <Text>{answer}</Text>
                    </div>
                  </div>
                </div>,
              ])}
            </div>
          </div>
        </Drawer>
      </Card>
    </Layout>
  );
};

export default LogPage;
