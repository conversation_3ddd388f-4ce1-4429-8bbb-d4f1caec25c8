'use client';
import React, { useRef, useState, useEffect } from 'react';
import { Tinyflow, TinyflowHandle } from '@tinyflow-ai/react';
import { Button, Space, Flex, message, Spin, Skeleton } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import { flowUpdate, flowDetail, getRunningParameters, getTinyFlowData, getLlmList } from '@/services/flow/api';
import { useParams } from 'next/navigation';
import { PluginNode } from './customNode/pluginNode';
import customNode from './customNode/index';
import WorkResultDrawer from './workResult/index';
import { PluginTools } from './pluginTool/index';
import '@tinyflow-ai/react/dist/index.css';
import styles from './index.modules.less';

const InsideFlow: React.FC = () => {
  const tinyflowRef = useRef<TinyflowHandle | null>(null);
  const { appId } = useParams();
  const [selectedItem, setSelectedItem] = useState<any>([]);
  const [changeNodeData, setChangeNodeData] = useState<any>();
  const [pluginOpen, setPluginOpen] = useState(false);
  const [parameters, setParameters] = useState<any[]>();
  const [saveLoading, setSaveLoading] = useState(false);
  const [runLoading, setRunLoading] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [flowData, setFlowData] = useState<any>();
  const [pageLoading, setPageLoading] = useState(false);
  const [showTinyflow, setShowTinyflow] = useState(false);
  const [llms, setLlms] = useState<{ id: any; title: any }[]>([]);

  //保存
  const saveFlow = async () => {
    setSaveLoading(true);
    const { errorCode } = await flowUpdate({
      id: appId as string,
      content: tinyflowRef.current?.getData(),
    });
    if (errorCode === 0) {
      message.success('保存成功');
    } else {
      message.error('保存失败');
    }
    setSaveLoading(false);
  };
  const handleChosen = (updateNodeData: any, value: any) => {
    console.log('handleChosen', updateNodeData);

    if (value) {
      setSelectedItem([value]);
    }
    setChangeNodeData(() => updateNodeData);
    setPluginOpen(true);
  };

  const getOptions = (options: { id: any; title: any }[]): { value: any; label: any }[] => {
    if (options) {
      return options.map((item) => ({
        value: item.id,
        label: item.title,
      }));
    }
    return [];
  };

  const customNodes: any = {
    ...customNode,
    'plugin-node': PluginNode({
      onChosen: handleChosen,
    }),
  };

  const provider = {
    llm: () => getOptions(llms),
  };

  //查询节点数据
  const fetchFlowDetail = async () => {
    const { errorCode, data } = await flowDetail(appId as string);
    if (errorCode === 0) {
      setFlowData(data.content);
    }
  };

  //查询大模型列表
  const fetchLlmList = async () => {
    const { errorCode, data } = await getLlmList();
    if (errorCode === 0) {
      setLlms(data);
    }
  };

  const showDrawer = () => {
    setDrawerOpen(true);
  };

  //试运行
  const showRunningParameters = async () => {
    setRunLoading(true);
    await flowUpdate({
      id: appId as string,
      content: tinyflowRef.current?.getData(),
    });
    const { errorCode, parameters } = await getRunningParameters(appId as string);
    if (errorCode === 0) {
      showDrawer();
      setParameters(parameters);
    }
    setRunLoading(false);
  };

  const onKeydown = (event: KeyboardEvent) => {
    // 检查是否按下的是 Ctrl + S 或 Command + S
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      // 阻止浏览器默认行为（打开保存对话框）
      event.preventDefault();

      // 保存数据
      saveFlow();
    }
  };

  useEffect(() => {
    fetchFlowDetail();
    fetchLlmList();

    document.addEventListener('keydown', onKeydown);
    return () => {
      document.removeEventListener('keydown', onKeydown);
    };
  }, []);

  useEffect(() => {
    if (llms) {
      setShowTinyflow(true);
    }
  }, [llms]);

  return (
    <>
      {/* 选择插件 */}
      <PluginTools
        selectedItem={selectedItem}
        open={pluginOpen}
        onCancel={() => setPluginOpen(false)}
        onSelectedItem={(item) => {
          console.log(item);
          setPluginOpen(false);
          setPageLoading(true);
          // 调用保存的 updateNodeData 函数
          if (changeNodeData) {
            getTinyFlowData(item.id).then((res) => {
              setPageLoading(false);
              changeNodeData(res.data);
            });
          }
          setSelectedItem([item.id]);
        }}
        onRemoveItem={() => {
          setPluginOpen(false);
          // 调用保存的 updateNodeData 函数
          if (changeNodeData) {
            changeNodeData({
              pluginId: '',
              pluginName: '',
              parameters: [],
              outputDefs: [],
            });
            setSelectedItem([]);
          }
        }}
      />
      {/* 试运行区域 */}
      {drawerOpen && (
        <WorkResultDrawer
          open={drawerOpen}
          parameters={parameters}
          onClose={() => setDrawerOpen(false)}
          id={appId as string}
        />
      )}
      {/* 工作流绘制区域 */}
      <div>
        <Flex justify="flex-end" style={{ marginTop: -40, paddingBottom: 20, borderBottom: '1px solid #eee' }}>
          <Space>
            <Button loading={runLoading} onClick={showRunningParameters}>
              <SendOutlined />
              试运行
            </Button>
            <Button type="primary" onClick={saveFlow} loading={saveLoading}>
              保存(Ctrl + s)
            </Button>
          </Space>
        </Flex>
        {showTinyflow ? (
          <Spin spinning={pageLoading}>
            <Tinyflow
              ref={tinyflowRef}
              data={flowData}
              customNodes={customNodes}
              hiddenNodes={['searchEngineNode', 'knowledgeNode']}
              style={{ height: 'calc(100vh - 10px)' }}
              className={styles['seres-flow']}
              provider={provider}
            />
          </Spin>
        ) : (
          <div style={{ padding: '20px' }}>
            <Skeleton active />
          </div>
        )}
      </div>
    </>
  );
};

export default InsideFlow;
