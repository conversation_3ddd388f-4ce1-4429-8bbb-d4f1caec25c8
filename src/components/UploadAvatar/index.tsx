'use client';
import { useState, useImperativeHandle, memo, forwardRef, useEffect, useMemo } from 'react';
import { Upload, message, Image } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadProps, UploadFile } from 'antd';
import ImgCrop from 'antd-img-crop';
import Cookies from 'js-cookie';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

interface IProps {}

interface UploadAvatarRef {
  imageUrl: string;
  setImageUrl: (value: string) => void;
}

const UploadAvatar = forwardRef<UploadAvatarRef, IProps>((_, ref) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [uploading, setUploading] = useState(false);
  const beforeUpload = (file: FileType) => {
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过 2MB!');
    }
    return isLt2M;
  };

  const uploadButton = useMemo(
    () => (
      <button style={{ border: 0, background: 'none' }} type="button">
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>{uploading ? '上传中...' : '请选择文件'}</div>
      </button>
    ),
    [uploading],
  );

  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList }) => {
    // 设置上传状态
    if (file.status === 'uploading') {
      setUploading(true);
    } else if (file.status === 'done' || file.status === 'error') {
      setUploading(false);
    }

    // 更新文件列表，但保持图片URL的稳定性
    const updatedFileList = newFileList.map((item) => {
      if (item.status === 'done' && item.response?.path && !item.url) {
        // 为已完成上传的文件设置URL，避免重复设置
        return { ...item, url: item.response.path };
      }
      return item;
    });

    setFileList(updatedFileList);

    // 只在状态真正改变时更新imageUrl
    if (file.status === 'done' && file.response?.path) {
      const newImageUrl = file.response.path as string;
      if (newImageUrl !== imageUrl) {
        setImageUrl(newImageUrl);
      }
    } else if (file.status === 'removed') {
      setImageUrl('');
    }
  };

  const onRemove = () => {
    setImageUrl('');
    setFileList([]);
  };

  const onPreview = async (file: UploadFile) => {
    let src = file.url as string;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as FileType);
        reader.onload = () => resolve(reader.result as string);
      });
    }
    setPreviewImage(src || (file.preview as string));
    setPreviewOpen(true);
  };

  useImperativeHandle(ref, () => ({
    setImageUrl,
    imageUrl,
  }));

  useEffect(() => {
    // 只在外部设置imageUrl时（比如编辑模式下初始化）才更新fileList
    // 避免在上传过程中的重复更新
    if (imageUrl && fileList.length === 0 && !uploading) {
      setFileList([
        {
          uid: imageUrl,
          name: imageUrl,
          status: 'done',
          url: imageUrl,
        } as UploadFile,
      ]);
    } else if (!imageUrl && fileList.length > 0 && !uploading) {
      // 只在imageUrl被清空且fileList不为空时才清空fileList
      setFileList([]);
    }
  }, [imageUrl, fileList.length, uploading]);

  return (
    <>
      <ImgCrop rotationSlider>
        <Upload
          accept="image/png, image/jpeg, image/jpg, image/bmp, image/webp, image/svg"
          action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
          headers={{ Authorization: Cookies.get('access_token') as string }}
          listType="picture-circle"
          fileList={fileList}
          onChange={handleChange}
          onPreview={onPreview}
          onRemove={onRemove}
          beforeUpload={beforeUpload}
          maxCount={1}
        >
          {fileList.length >= 1 ? null : uploadButton}
        </Upload>
      </ImgCrop>

      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </>
  );
});

UploadAvatar.displayName = 'UploadAvatar';
export default memo(UploadAvatar);
