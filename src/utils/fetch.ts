import Cookies from 'js-cookie';

// 流式响应处理器类型
export interface StreamHandler {
  onMessage?: (data: any) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

// 流式请求配置
export interface StreamRequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  handler: StreamHandler;
}

/**
 * 发送流式请求
 * @param config 请求配置
 */
export async function fetchStream(config: StreamRequestConfig): Promise<void> {
  const { url, method = 'POST', headers = {}, body, handler } = config;

  try {
    // 构建完整的URL
    const fullUrl = url.startsWith('http') ? url : `${process.env.NEXT_PUBLIC_AI_BASE_URL}${url}`;

    // 默认请求头
    const defaultHeaders = {
      'Content-Type': 'application/json',
      Authorization: Cookies.get('access_token') || '',
      ...headers,
    };

    console.log('发送流式请求:', {
      url: fullUrl,
      method,
      headers: defaultHeaders,
      body,
    });

    // 发送请求
    const response = await fetch(fullUrl, {
      method,
      headers: defaultHeaders,
      body: body ? JSON.stringify(body) : undefined,
    });

    console.log('收到响应:', {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('请求失败:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    // 处理流式响应
    await handleStreamResponse(response, handler);
  } catch (error) {
    console.error('流式请求错误:', error);
    handler.onError?.(error as Error);
  }
}

/**
 * 处理流式响应
 * @param response 响应对象
 * @param handler 处理器
 */
async function handleStreamResponse(response: Response, handler: StreamHandler): Promise<void> {
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('无法获取响应流');
  }

  const decoder = new TextDecoder();
  let buffer = '';

  try {
    console.log('开始读取流数据...');

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        console.log('流数据读取完成');
        handler.onComplete?.();
        break;
      }

      // 解码数据块
      const chunk = decoder.decode(value, { stream: true });
      console.log('接收到数据块:', chunk);
      console.log('数据块长度:', chunk.length);
      console.log('数据块类型:', typeof chunk);

      buffer += chunk;
      console.log('当前缓冲区内容:', buffer);

      // 按行分割数据
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      console.log(`分割得到 ${lines.length} 行数据，剩余缓冲区:`, buffer);

      // 处理每一行
      for (const line of lines) {
        const trimmedLine = line.trim();
        console.log('处理行数据:', `"${trimmedLine}"`);

        if (!trimmedLine) {
          console.log('跳过空行');
          continue;
        }

        try {
          // 尝试解析数据
          let dataMessage = '';

          // 检查是否是SSE格式
          if (trimmedLine.startsWith('data: ')) {
            dataMessage = trimmedLine.replace('data: ', '').trim();
          } else {
            // 直接作为JSON数据处理
            dataMessage = trimmedLine;
          }

          if (!dataMessage || dataMessage === '[DONE]') {
            continue;
          }

          // 解析JSON数据
          const parsed = JSON.parse(dataMessage);
          console.log('解析到数据:', parsed);

          // 调用消息处理器
          handler.onMessage?.(parsed);
        } catch (parseError) {
          console.error('JSON解析失败:', parseError);
          console.error('原始数据:', trimmedLine);
          console.error('数据类型:', typeof trimmedLine);
          console.error('数据长度:', trimmedLine.length);
          console.error('数据的前100个字符:', trimmedLine.substring(0, 100));

          // 检查是否包含特殊字符或编码问题
          console.error(
            '数据的字符编码:',
            Array.from(trimmedLine)
              .map((char) => char.charCodeAt(0))
              .slice(0, 20),
          );

          // 尝试清理数据中的特殊字符
          const cleanedData = trimmedLine
            .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
            .replace(/^\uFEFF/, '') // 移除BOM
            .trim();

          if (cleanedData !== trimmedLine) {
            console.log('清理后的数据:', cleanedData);
            try {
              const parsed = JSON.parse(cleanedData);
              console.log('清理后解析成功:', parsed);
              handler.onMessage?.(parsed);
              continue; // 成功解析，继续下一行
            } catch (cleanError) {
              console.error('清理后仍然解析失败:', cleanError);
            }
          }

          // 尝试处理多个JSON对象连接的情况
          if (trimmedLine.includes('}{')) {
            console.log('检测到多个JSON对象，尝试分割处理');
            const jsonObjects = trimmedLine.split('}{');
            let hasSuccess = false;

            for (let i = 0; i < jsonObjects.length; i++) {
              let jsonStr = jsonObjects[i];
              if (i > 0) jsonStr = '{' + jsonStr;
              if (i < jsonObjects.length - 1) jsonStr = jsonStr + '}';

              console.log(`尝试解析分割对象 ${i + 1}:`, jsonStr);

              try {
                const parsed = JSON.parse(jsonStr);
                console.log(`分割解析成功 ${i + 1}:`, parsed);
                handler.onMessage?.(parsed);
                hasSuccess = true;
              } catch (splitError) {
                console.error(`分割解析失败 ${i + 1}:`, splitError);
              }
            }

            if (hasSuccess) {
              continue; // 至少有一个成功解析，继续下一行
            }
          }

          // 尝试查找JSON片段
          const jsonMatch = trimmedLine.match(/\{.*\}/);
          if (jsonMatch) {
            console.log('尝试提取JSON片段:', jsonMatch[0]);
            try {
              const parsed = JSON.parse(jsonMatch[0]);
              console.log('JSON片段解析成功:', parsed);
              handler.onMessage?.(parsed);
              continue;
            } catch (fragmentError) {
              console.error('JSON片段解析失败:', fragmentError);
            }
          }

          // 如果所有尝试都失败，记录详细信息但不中断流程
          console.warn('所有解析尝试都失败，跳过此数据行');
        }
      }
    }
  } catch (error) {
    console.error('流处理错误:', error);
    handler.onError?.(error as Error);
  } finally {
    reader.releaseLock();
  }
}

/**
 * 调试用：直接输出原始流数据
 * @param data 请求数据
 * @param handler 处理器
 */
export async function debugStream(
  data: { id: string; variables?: Record<string, any> },
  handler: StreamHandler,
): Promise<void> {
  const url = `${process.env.NEXT_PUBLIC_AI_BASE_URL}/ai-flow/api/v1/aiWorkflow/tryRunningStream`;

  console.log('=== 调试模式：开始流式请求 ===');
  console.log('请求URL:', url);
  console.log('请求数据:', data);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: Cookies.get('access_token') || '',
      },
      body: JSON.stringify(data),
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('请求失败:', errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let chunkCount = 0;

    try {
      while (true) {
        const { done, value } = await reader.read();
        chunkCount++;

        if (done) {
          console.log('=== 流数据读取完成 ===');
          handler.onComplete?.();
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        console.log(`=== 数据块 ${chunkCount} ===`);
        console.log('原始数据:', chunk);
        console.log('数据长度:', chunk.length);
        console.log(
          '十六进制:',
          Array.from(chunk)
            .map((c) => c.charCodeAt(0).toString(16).padStart(2, '0'))
            .join(' '),
        );

        // 尝试直接解析整个数据块
        try {
          const parsed = JSON.parse(chunk);
          console.log('整块解析成功:', parsed);
          handler.onMessage?.(parsed);
        } catch (error) {
          console.log('整块解析失败，尝试按行处理');

          // 按行处理
          const lines = chunk.split('\n');
          lines.forEach((line, index) => {
            const trimmed = line.trim();
            if (trimmed) {
              console.log(`行 ${index + 1}:`, `"${trimmed}"`);
              try {
                const parsed = JSON.parse(trimmed);
                console.log(`行 ${index + 1} 解析成功:`, parsed);
                handler.onMessage?.(parsed);
              } catch (lineError) {
                console.log(`行 ${index + 1} 解析失败:`, lineError);
              }
            }
          });
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error('调试流请求错误:', error);
    handler.onError?.(error as Error);
  }
}

/**
 * 工作流流式执行请求
 * @param data 请求数据
 * @param handler 处理器
 */
export async function tryRunningStream(
  data: { id: string; variables?: Record<string, any> },
  handler: StreamHandler,
): Promise<void> {
  // 在调试模式下使用调试函数
  if (process.env.NODE_ENV === 'development') {
    return debugStream(data, handler);
  }

  return fetchStream({
    url: '/ai-flow/api/v1/aiWorkflow/tryRunningStream',
    method: 'POST',
    body: data,
    handler,
  });
}
