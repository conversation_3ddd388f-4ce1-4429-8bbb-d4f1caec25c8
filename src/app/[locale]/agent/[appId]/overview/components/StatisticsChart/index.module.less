.statisticsCard {

  :global(.ant-card-body) {
    padding: 0 !important;
    display: flex;
    flex-direction: column;
  }
}

.titleRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 16px;
  border-bottom: 1px solid #e9e9e9;
}

.titleWrapper {
  display: flex;
  align-items: center;
}

.title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  display: flex;
  gap: 10px
}

.infoIcon {
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
}

.period {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}