'use client';
import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Upload, message, Collapse, Spin } from 'antd';
import { SendOutlined, UploadOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import Cookies from 'js-cookie';
import { detail } from '@/services/flow/api';
import ReactJsonView from '@/components/JsonView';
import { sortNodes } from './workUtil';
import { useSse } from './useSse';

interface IProps {
  id: string;
  open: boolean;
  onClose: () => void;
  parameters: any;
}

const WorkResultDrawer: React.FC<IProps> = ({ id, open, onClose, parameters }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [executeResult, setExecuteResult] = useState<object>({});
  const [collapseItems, setCollapseItems] = useState<any[]>([]);
  const [activeCol, setActiveCol] = useState('')


  const { start: runWithStream } = useSse("/ai-flow/api/v1/aiWorkflow/tryRunningStream");
  const onFinish = async (values: any) => {
    collapseItems.map((item: any) => {
      item.extra = ""
      item.children = ""
      item.style = { background: '' }
    })
    setCollapseItems([...collapseItems])
    setSubmitLoading(true)
    runWithStream({
      data: {
        id: id,
        variables: values
      },
      onMessage: (msg: any) => {
        handleStreamMsg(msg)
      },
      onFinished: () => {
        setSubmitLoading(false)
      },
    })
  };

  const handleStreamMsg = (msg: any) => {
    //console.log(msg)
    if (msg.status === 'execOnce') {
      message.warning("流程已执行完毕，请重新发起。")
    }
    if (msg.execResult) {
      message.success('执行完毕')
      setExecuteResult(msg.execResult)
    }
    if (msg.status === 'error') {
      message.error('执行错误')
      setExecuteResult(msg)
      collapseItems.map((item: any) => {
        item.extra = <Spin indicator={<ExclamationCircleOutlined style={{ color: "#EABB00" }} />} />
      })
      setCollapseItems([...collapseItems])
    }
    if (msg.nodeId && msg.status) {
      collapseItems.map((item: any) => {
        const nodeType = item.original.type
        if (item.key == msg.nodeId) {
          if (msg.status === 'start') {
            // 非确认节点不设置起始状态，因为恢复执行后会再次执行此节点
            if ("confirmNode" !== nodeType) {
              item.extra = <Spin indicator={<LoadingOutlined />} />
              item.children = ""
            }
          }
          if (msg.status === 'end') {
            // 确认节点如果子项不为空就不再重新渲染，因为恢复执行后会再次执行此节点，重新渲染的话就是JSON字符串了。
            if ("confirmNode" === nodeType && item.children) {
              return
            }
            item.extra = <Spin indicator={<CheckCircleOutlined style={{ color: 'green' }} />} />
            item.children = msg.res ?
              <div style={{ wordWrap: "break-word", }}>
                <ReactJsonView value={msg.res} minHeight={10} background={'#ffffff'} />
              </div> : ""
          }
          if (msg.status === 'nodeError') {
            item.extra = <Spin indicator={<CloseCircleOutlined style={{ color: 'red' }} />} />
            item.children = <ReactJsonView value={msg.errorMsg} minHeight={10} background={'#ffffff'} />
          }
          if (msg.status === 'confirm') {
            message.success("有待确认的内容，请先确认！")
            setActiveCol(msg.nodeId)
          }
        }
      })
      setCollapseItems([...collapseItems])
    }
  }


  const onFinishFailed = (errorInfo: any) => {
    setSubmitLoading(false);
    message.error('失败：' + errorInfo);
    console.log('Failed:', errorInfo);
  };

  const getParameters = async () => {
    const resp = await detail(id);
    const nodeJson = JSON.parse(resp.data?.content);
    const nodes = sortNodes(nodeJson)
    setCollapseItems(nodes)
  };

  useEffect(() => { getParameters() }, [id])

  return (
    <Drawer width={640} title="请输入参数" placement="right" closable={false} onClose={onClose} open={open}>
      <Form
        form={form}
        name="basic"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        {parameters &&
          parameters.map((item: any) => {
            let inputComponent: React.ReactNode;
            switch (item.dataType) {
              case 'File':
                inputComponent = (
                  <Upload
                    maxCount={1}
                    action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
                    headers={{ Authorization: Cookies.get('access_token') as string }}
                    onChange={({ file }) => {
                      if (file.status === 'done') {
                        let url = file.response?.response.url;
                        if (url.indexOf('http') < 0) {
                          url = window.location.origin + url;
                        }
                        form.setFieldsValue({
                          [item.name]: url,
                        });
                      }
                    }}
                  >
                    <Button icon={<UploadOutlined />}>上传</Button>
                  </Upload>
                );
                break;
              default:
                inputComponent = <Input />;
            }

            return (
              <Form.Item
                key={item.name}
                label={item.name}
                name={item.name}
                rules={[{ required: item.required }]}
                extra={item.description}
              >
                {inputComponent}
              </Form.Item>
            );
          })}

        <Form.Item wrapperCol={{ offset: 4, span: 18 }}>
          <Button disabled={submitLoading} loading={submitLoading} type="primary" htmlType="submit">
            <SendOutlined /> 开始运行
          </Button>
        </Form.Item>
      </Form>

      <div>
        <div>执行结果：</div>
        <div style={{ padding: 10, backgroundColor: '#F5F6F9' }}>
          <ReactJsonView value={executeResult} minHeight={100} />
        </div>
      </div>

      <div style={{ marginTop: 20 }}>
        <div>执行步骤：</div>
        <div>
          <Collapse activeKey={activeCol} items={collapseItems} onChange={(k: any) => {
            setActiveCol(k)
          }} size="small" />
        </div>
      </div>
    </Drawer>
  );
};

export default WorkResultDrawer;
