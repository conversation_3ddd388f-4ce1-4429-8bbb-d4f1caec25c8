'use client';
import React from 'react';
import { Tag, Space, Tooltip, Flex } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { useRouter } from '@bprogress/next/app';
import CommonLayout from '../Layout';

interface IProps {
  children: React.ReactNode;
  curActive: string;
  defaultOpen?: string[];
  agentDetailDom?: React.ReactNode;
  detailData: any;
}
const AgentDetailLayout: React.FC<IProps> = ({ children, curActive, defaultOpen = ['/'], detailData }) => {
  const router = useRouter();
  // 定义类型颜色
  const typeColor = (type: string) => {
    switch (type) {
      case 'inter':
        return { color: '#ff4d85', background: 'rgba(255, 77, 133, 0.1)' };
      case 'dify':
        return { color: '#00b8d4', background: 'rgba(0, 184, 212, 0.1)' };
      case 'Other':
        return { color: '#9c27b0', background: 'rgba(156, 39, 176, 0.1)' };
      default:
        return { color: '#2080ff', background: 'rgba(32, 128, 255, 0.1)' };
    }
  };

  const colorStyle = typeColor(detailData.source);

  // 定义类型颜色
  const applyTypeColor = (applyType: string) => {
    switch (applyType) {
      case 'workflow':
        return { color: '#2f54eb', background: 'rgba(47, 84, 235, 0.15)' };
      case 'chat':
        return { color: '#fa8c16', background: 'rgba(250, 140, 22, 0.15)' };
      default:
        return { color: '#2080ff', background: 'rgba(32, 128, 255, 0.1)' };
    }
  };

  const applyTypeColorStyle = applyTypeColor(detailData.applyType);

  const unCollapsedDom = () => {
    return (
      <div
        style={{
          display: 'flex',
          paddingTop: 15,
          paddingBottom: 15,
          marginBottom: 20,
          marginLeft: 10,
          marginRight: 10,
          color: '#fff',
          borderBottom: '1px solid hsla(0,0%,100%,.3)',
        }}
      >
        <LeftOutlined
          style={{
            fontSize: 24,
            marginRight: 10,
            cursor: 'pointer',
            transition: 'color 0.2s',
          }}
          onMouseEnter={(e) => (e.currentTarget.style.color = '#1890ff')}
          onMouseLeave={(e) => (e.currentTarget.style.color = '#fff')}
          onClick={() => router.push(`/agent`)}
        />
        <div>
          <Space style={{ marginBottom: 5 }}>
            <Tooltip placement="bottom" title={detailData.name}>
              <div
                style={{
                  color: '#fff',
                  fontSize: 16,
                  maxWidth: 100,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {detailData.name}
              </div>
            </Tooltip>
          </Space>
          <Flex gap="4px 0" style={{ marginBottom: 5 }}>
            <Tag
              style={{
                color: colorStyle.color,
                background: colorStyle.background,
                border: `1px solid ${colorStyle.color}`,
                borderRadius: '4px',
              }}
            >
              {detailData.sourceName}
            </Tag>
            <Tag
              style={{
                color: applyTypeColorStyle.color,
                background: applyTypeColorStyle.background,
                border: `1px solid ${applyTypeColorStyle.color}`,
                borderRadius: '4px',
              }}
            >
              {detailData.applyTypeName}
            </Tag>
          </Flex>
          <Tooltip placement="bottom" title={detailData.description}>
            <div
              style={{
                maxWidth: 150,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                color: '#84868c',
              }}
            >
              {detailData.description ?? '暂无描述'}
            </div>
          </Tooltip>
        </div>
      </div>
    );
  };

  const collapsedDom = () => {
    return (
      <div
        style={{
          display: 'flex',
          height: 78,
          justifyContent: 'center',
          marginBottom: 20,
          marginLeft: 10,
          marginRight: 10,
          color: '#fff',
          borderBottom: '1px solid hsla(0,0%,100%,.3)',
        }}
      >
        <LeftOutlined
          style={{
            fontSize: 24,
            marginRight: 10,
            cursor: 'pointer',
            transition: 'color 0.2s',
          }}
          onMouseEnter={(e) => (e.currentTarget.style.color = '#1890ff')}
          onMouseLeave={(e) => (e.currentTarget.style.color = '#fff')}
          onClick={() => router.push(`/agent`)}
        />
      </div>
    );
  };

  const agentDetailDom = (collapsed: boolean) => {
    // if (collapsed) {
    //   //左侧菜单折叠展示
    //   return collapsedDom();
    // }
    // //左侧菜单展开展示
    // return unCollapsedDom();
    return (
      <div style={{ overflow: 'hidden', transition: 'width 0.3s ease-in-out' }}>
        {collapsed ? collapsedDom() : unCollapsedDom()}
      </div>
    );
  };

  return (
    <CommonLayout
      curActive={curActive}
      defaultOpen={defaultOpen}
      agentDetailDom={agentDetailDom}
      agentDetailData={detailData}
    >
      {children}
    </CommonLayout>
  );
};

export default AgentDetailLayout;
