import { create } from 'zustand';
import { persist, devtools, createJSONStorage } from 'zustand/middleware';
import { getUserList, fetchUserInfo } from '@/services/user/api';

interface UserInfo {
  userInfo: {
    [key: string]: any;
  };
  menuList: any[];
  getMenuList: () => Promise<void>;
  getUserInfo: (callback?: () => void) => Promise<void>;
}

const useStore = create(
  devtools(
    persist<UserInfo>(
      (set) => ({
        userInfo: {},
        menuList: [],

        getUserInfo: async (callback) => {
          const { success, resp } = await fetchUserInfo();
          if (success) {
            set({ userInfo: resp[0] });
            callback && callback();
          }
        },

        getMenuList: async () => {
          const { success, resp } = await getUserList();
          if (success) {
            set({ menuList: resp });
          }
        },
      }),
      {
        name: 'seres-storage', // unique name
        storage: createJSONStorage(() => localStorage), // 明确指定使用 localStorage
      },
    ),
  ),
);

export default useStore;
