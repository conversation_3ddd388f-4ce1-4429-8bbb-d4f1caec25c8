'use client';
import { useState, useEffect, useCallback } from 'react';
import { Card, Input, Button, Table, Space, message, Typography, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { SearchOutlined, UploadOutlined, ReloadOutlined, LeftOutlined } from '@ant-design/icons';

import AddFileModal from './addFileModal';
import zhCN from 'antd/es/locale/zh_CN';
import debounce from 'lodash-es/debounce';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { getDocList, analysis, deleteDocument } from '@/services/konwledge/api';
import dayjs from 'dayjs';
import axios from 'axios';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';

const { Title } = Typography;
const { confirm } = Modal;

interface ListTemplate {
  id: number;
  name: string;
  fileSize: string;
  fileType: number;
  status: number;
  createTime: string;
  downloadUrl: string;
}

const ANALYSIS_MAP = [
  {
    label: '未解析',
    value: 1,
    color: '#ff4d4f',
  },
  {
    label: '解析中',
    value: 2,
    color: '#faad14',
  },
  {
    label: '解析成功',
    value: 3,
    color: '#52c41a',
  },
  {
    label: '解析失败',
    value: 4,
    color: '#f5222d',
  },
  {
    label: '解析已删除',
    value: 5,
    color: '#d9d9d9',
  },
];

const FILE_TYPE_MAP = [
  {
    label: 'xlsx',
    value: 1,
  },
  {
    label: 'pdf',
    value: 2,
  },
];

const KonwledgeFilePage: React.FC = () => {
  const router = useRouter();
  const { id } = useParams();
  const searchParams = useSearchParams();
  const { userInfo } = useStore();

  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ListTemplate[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Partial<ListTemplate>>();
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    handleSearch();
  }, []);

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '文件名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '数据量',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
    },
    {
      title: '文件格式',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 120,
      render: (_, record) => {
        const fileType = FILE_TYPE_MAP.find((item) => item.value === record.fileType);
        return fileType ? fileType.label : '-';
      },
    },

    {
      title: '上传时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 120,
      render: (_, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm') : '-'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (_, record) => {
        const analysis = ANALYSIS_MAP.find((item) => item.value === record.status);
        return analysis ? <span style={{ color: analysis.color }}>{analysis.label}</span> : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          {roleJudgment(userInfo, 'KNOWLEDGE_DETAIL_DOWNLOAD') && (
            <a
              onClick={() => {
                handleDownload(record);
              }}
            >
              下载
            </a>
          )}
          {roleJudgment(userInfo, 'KNOWLEDGE_DETAIL_DELETE') ? (
            [2, 3].includes(record.status) ? (
              <a
                style={{
                  cursor: 'not-allowed',
                  color: 'var(--ant-color-text-disabled)',
                }}
              >
                解析
              </a>
            ) : (
              <a onClick={() => handleAnalysis(record)}>解析</a>
            )
          ) : null}

          {/* <a>查看切片</a>
          <a>修改配置</a> */}
          {roleJudgment(userInfo, 'KNOWLEDGE_DETAIL_DELETE') && (
            <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
              删除
            </a>
          )}
        </Space>
      ),
    },
  ];

  const handleSearch = async (name?: string) => {
    try {
      setLoading(true);
      const response = await getDocList({
        params: { name, knowId: id as string },
        current,
        pageSize,
      });
      if (response.success) {
        setData(response.resp[0].list);
        setTotal(response.resp[0]?.pagination?.total);
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setModalOpen(true);
  };

  //下载
  const handleDownload = async (record: ListTemplate) => {
    const download = (res: any, filename: string) => {
      // 创建blob对象，解析流数据
      const blob = new Blob([res.data]);
      const a = document.createElement('a');
      const URL = window.URL || window.webkitURL;
      // 根据解析后的blob对象创建URL 对象
      const herf = URL.createObjectURL(blob);
      // 下载链接
      a.href = herf;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      // 在内存中移除URL 对象
      window.URL.revokeObjectURL(herf);
    };
    const res = await axios.get(record.downloadUrl, {
      responseType: 'blob',
    });
    download(res, record.name);
  };

  const handleAnalysis = async (record: ListTemplate) => {
    const respData = await analysis(record.id);
    if (respData.success) {
      message.success('解析中...');
      handleSearch(searchName);
    } else {
      message.error(respData.msg || '解析失败');
    }
  };

  const handleDelete = (record: ListTemplate) => {
    confirm({
      title: '确认删除',
      content: `是否删除该文件：${record.name}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        try {
          const respData = await deleteDocument(record.id);
          if (respData.success) {
            message.success('删除成功');
            handleSearch(searchName); // 刷新列表
          } else {
            message.success('删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleModalSuccess = () => {
    setModalOpen(false);
    handleSearch(searchName);
  };

  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  const changeHandle = useCallback(
    debounce((value: string) => {
      setSearchName(value);
      handleSearch(value);
    }, 500),
    [],
  );
  return (
    <div>
      <Card variant="borderless">
        <div
          style={{
            marginBottom: 24,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Space>
            <Input
              style={{ width: 250 }}
              size="middle"
              placeholder="请输入文件名称"
              onChange={(e) => setSearchName(e.target.value)}
              prefix={<SearchOutlined />}
              allowClear
            />
            <Button icon={<ReloadOutlined />} onClick={() => handleSearch(searchName)}></Button>
          </Space>

          <Space>
            {/* <Button>检索测试</Button> */}
            {roleJudgment(userInfo, 'KNOWLEDGE_DETAIL_UPLOAD') && (
              <Button type="primary" icon={<UploadOutlined />} onClick={handleAdd}>
                导入文件
              </Button>
            )}
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1300 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            defaultPageSize: 10,
            locale: zhCN.Pagination, // 设置分页的本地化为中文
            pageSize,
            onChange: PageChange,
            current,
            onShowSizeChange: PageChange,
            total,
          }}
        />
      </Card>

      <AddFileModal
        open={modalOpen}
        onCancel={() => setModalOpen(false)}
        onSuccess={handleModalSuccess}
        id={id as string}
      />
    </div>
  );
};

export default KonwledgeFilePage;
