import React, { useState, useEffect } from 'react';
import { Avatar, Menu, Space, Button, Typography } from 'antd';
import styles from '../../page.module.less';
import type { GetProp, MenuProps } from 'antd';
import classNames from 'classnames';
import { useSearchParams } from 'next/navigation';
type MenuItem = GetProp<MenuProps, 'items'>[number];
const { Paragraph, Text } = Typography;
interface IProps {
  items: MenuItem[];
  pageId: string;
  handleSelect: (selectedItem: { key: string }) => void;
}

const KnowledgeSidebar: React.FC<IProps> = ({ handleSelect, pageId, items }) => {
  // 可选：是否需要折叠功能可按需取消注释
  // const [collapsed, setCollapsed] = useState<boolean>(false);
  const searchParams = useSearchParams();
  return (
    <div className={styles.sidebarWrapper}>
      <div className={styles.sidebarTop}>
        <Space size={8} direction="vertical">
          <Avatar size={64} src={'https://example.com/avatar.png'} />
          <div className={styles.knowledgeTitle}>{searchParams.get('name')}</div>
        </Space>

        <Paragraph ellipsis={{ rows: 1 }} className={styles.knowledgeDescription}>
          {searchParams.get('content')}
        </Paragraph>
      </div>
      <div className={styles.divider}></div>
      <div className={styles.menuWrapper}>
        <Menu
          selectedKeys={[pageId]}
          mode="inline"
          className={classNames(styles.menu)}
          inlineCollapsed={false}
          items={items}
          onSelect={handleSelect}
        />
        {/* <Button onClick={() => setCollapsed(!collapsed)} style={{ marginTop: 16 }}>
          {collapsed ? '展开' : '收起'}
        </Button> */}
      </div>
    </div>
  );
};

export default KnowledgeSidebar;
