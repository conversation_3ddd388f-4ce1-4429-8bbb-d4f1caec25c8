'use client';
import React, { useState, useEffect } from 'react';
import { Button, Col, Empty, List, Modal, Pagination, Row, Spin, message } from 'antd';
import { ModalProps } from 'antd/es/modal/interface';
import { aiPluginList } from '@/services/tool/api';
import Search from 'antd/es/input/Search';
import { useRouter } from 'next/navigation';
import { AccordionItem } from '@/components/Accordion/Accordion';
import { ToolItem } from '@/types/tool';

export type PluginToolsProps = {
  selectedItem?: any[];
  onSelectedItem?: (item: any) => void;
  onRemoveItem?: (item: any) => void;
} & ModalProps;
export const PluginTools: React.FC<PluginToolsProps> = (props) => {
  const router = useRouter();
  const [total, setTotal] = useState<number>(0);
  const [toolList, setToolList] = useState<ToolItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [pageParams, setPageParams] = useState({
    pageNumber: 1,
    pageSize: 10,
    name: '',
  });
  const selected = props.selectedItem;
  const [selectedIndex, setSelectedIndex] = useState(-1);

  //获取插件数据
  const getData = async (params: { pageNumber: number; pageSize: number; name?: string }) => {
    setLoading(true);
    const { data, errorCode } = await aiPluginList(params);
    if (errorCode === 0) {
      setToolList(data?.records || []);
      setTotal(data?.totalRow || 0);
    } else {
      message.error('获取工具列表失败');
    }
    setLoading(false);
  };

  useEffect(() => {
    getData(pageParams);
  }, []);

  //搜索
  const onSearch = (value: string) => {
    setPageParams({
      ...pageParams,
      name: value,
    });
    getData({
      pageNumber: 1,
      pageSize: pageParams.pageSize,
      name: value,
    });
  };

  //切换分页
  const changeCurrent = (page: number) => {
    setPageParams({
      ...pageParams,
      pageNumber: page,
      pageSize: pageParams.pageSize,
    });
    getData({
      ...pageParams,
      pageNumber: page,
    });
  };

  return (
    <Modal title={'选择插件'} footer={null} {...props} width={'1000px'} height={'600px'}>
      <Row gutter={16} style={{ width: '100%' }}>
        <Col span={6} style={{ backgroundColor: '#f5f5f5', paddingTop: '10px' }}>
          <Search style={{ marginBottom: '10px' }} placeholder="搜索" onSearch={onSearch} />
          <Button
            block
            type={'primary'}
            onClick={() => {
              router.push(`/llmsTool/plugin`);
            }}
          >
            创建插件
          </Button>
        </Col>
        <Col span={18}>
          <Spin spinning={loading}>
            <div>
              {total > 0 ? (
                toolList.map((item: any, index: number) => {
                  return (
                    <AccordionItem
                      key={index}
                      icon={item.icon}
                      title={item.name}
                      description={item.description}
                      isActive={selectedIndex == index}
                      clickItem={() => {
                        if (selectedIndex == index) {
                          setSelectedIndex(-1);
                        } else {
                          setSelectedIndex(index);
                        }
                      }}
                    >
                      <List
                        dataSource={item.tools}
                        renderItem={(item: any, index) => (
                          <List.Item
                            key={index}
                            actions={
                              selected?.includes(item.id)
                                ? [
                                    <Button
                                      color="danger"
                                      key={'remove' + index}
                                      variant="outlined"
                                      onClick={() => {
                                        props.onRemoveItem?.(item);
                                      }}
                                    >
                                      移除
                                    </Button>,
                                  ]
                                : [
                                    <Button
                                      key={'select' + index}
                                      onClick={() => {
                                        props.onSelectedItem?.(item);
                                      }}
                                    >
                                      选择
                                    </Button>,
                                  ]
                            }
                          >
                            <List.Item.Meta title={item.name} description={item.description} />
                          </List.Item>
                        )}
                      />
                    </AccordionItem>
                  );
                })
              ) : (
                <Empty style={{ height: '100%' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
              <div style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                <Pagination
                  onChange={changeCurrent}
                  align="end"
                  defaultCurrent={pageParams.pageNumber}
                  pageSize={pageParams.pageSize}
                  total={total}
                />
              </div>
            </div>
          </Spin>
        </Col>
      </Row>
    </Modal>
  );
};
