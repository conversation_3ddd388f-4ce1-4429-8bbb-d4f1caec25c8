'use client';
import Layout from '@/components/Layout';
import useStore from '@/store/useStore';
import { Button, Flex, Form, Input, message, Typography } from 'antd';
import { useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import StyleSheet from '../page.module.less';
const { Title } = Typography;
const { TextArea } = Input;
import { getPromptEvaluator, getPromptOptimizerAiDetail } from '@/services/prompt/api';

function PromptPage() {
  const [state, setState] = useState({
    id: '',
    content: '',
    evaluator: '',
    llm_scores: {
      clarity: 0,
      specificity: 0,
      effectiveness: 0,
      complexity: 0,
    },
    overall_final_scores: {
      clarity: 0,
      specificity: 0,
      effectiveness: 0,
      complexity: 0,
    },
    overall_llm_scores: 0,
    overall_rule_scores: 0,
    overall_score: 0,
    rule_scores: {
      clarity: 0,
      specificity: 0,
      effectiveness: 0,
      complexity: 0,
    },
    weights: {},
  });
  const searchParams = useSearchParams();
  const id = searchParams.get('id'); // 获取 query 参数 id
  const [loading, setLoading] = useState(false);
  const { userInfo } = useStore();
  const flag = useRef(false);
  const handleGenerate = async () => {
    if (!state.content) {
      message.error('请填写提示词');
      return;
    }
    try {
      setLoading(true);
      const obj = {
        conversationId: 'conversationId', // 考虑从配置文件中读取
        messages: [
          {
            question: 'question',
            role: 'user', // 固定角色名，考虑常量化
            content: state.content,
            id: '4',
          },
        ],
        userId: userInfo.id.toString(),
        stream: false,
      };
      const result = await getPromptEvaluator(obj);
      if (result.success && result.resp.length > 0) {
        const {
          evaluator,
          llm_scores,
          overall_final_scores,
          overall_llm_scores,
          overall_rule_scores,
          overall_score,
          rule_scores,
          weights,
        } = result.resp[0];
        flag.current = true;
        setState((pre) => ({
          ...pre,
          ...{
            evaluator,
            llm_scores,
            overall_final_scores,
            overall_llm_scores,
            overall_rule_scores,
            overall_score,
            rule_scores,
            weights,
          },
        }));
      } else {
        flag.current = false;
        message.error(result.message || '查询失败');
      }
    } catch (error) {
      flag.current = false;
      message.error('查询失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (id) {
      getPromptOptimizerAiDetail(id).then((res) => {
        const { success, resp } = res;
        if (success) {
          setState((pre) => ({
            ...pre,
            content: resp[0]?.output,
          }));
        }
      });
    }
  }, [id]);
  return (
    <Layout curActive="/prompt/assessment">
      <Title level={2} style={{ marginBottom: 24 }}>
        提示词评估
      </Title>
      <Form layout="vertical">
        <Form.Item>
          <TextArea
            disabled={loading}
            value={state.content}
            rows={10}
            placeholder="请输入原始提示词"
            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
              setState((prev) => ({ ...prev, content: e.target.value }))
            }
          />
        </Form.Item>
        <Form.Item>
          <Flex align="flex-start" justify="space-between">
            <span></span>
            <Button type="primary" onClick={handleGenerate} loading={loading}>
              评估
            </Button>
          </Flex>
        </Form.Item>
        <Form.Item>
          <div className={StyleSheet.assessment}>
            <div className={StyleSheet.assessmentDiv1}>{!loading && flag.current && <pre>{state.evaluator}</pre>}</div>
            <div className={StyleSheet.assessmentDiv2}>
              {!loading && flag.current && (
                <pre>
                  <div style={{ textAlign: 'center' }}>规则评估</div>
                  <div>计算清晰度（Clarity）：{state.rule_scores.clarity}</div>
                  <div>计算具体性（Specificity）：{state.rule_scores.specificity}</div>
                  <div>计算有效性（Effectiveness）：{state.rule_scores.effectiveness}</div>
                  <div>计算结构复杂度（Complexity）：{state.rule_scores.complexity}</div>
                  <div>规则评估综合得分：{state.overall_rule_scores}</div>
                </pre>
              )}
            </div>
            <div className={StyleSheet.assessmentDiv3}>
              {!loading && flag.current && (
                <pre>
                  <div style={{ textAlign: 'center' }}> 模型评估</div>
                  <div>计算清晰度（Clarity）：{state.llm_scores.clarity}</div>
                  <div>计算具体性（Specificity）：{state.llm_scores.specificity}</div>
                  <div>计算有效性（Effectiveness）：{state.llm_scores.effectiveness}</div>
                  <div>计算结构复杂度（Complexity）：{state.llm_scores.complexity}</div>
                  <div>规则评估综合得分：{state.overall_llm_scores}</div>
                </pre>
              )}
            </div>
            <div className={StyleSheet.assessmentDiv4}>
              {!loading && flag.current && (
                <pre>
                  <div style={{ textAlign: 'center' }}>模型和规则混合评估</div>
                  <div>计算清晰度（Clarity）：{state.overall_final_scores.clarity}</div>
                  <div>计算具体性（Specificity）：{state.overall_final_scores.specificity}</div>
                  <div>计算有效性（Effectiveness）：{state.overall_final_scores.effectiveness}</div>
                  <div>计算结构复杂度（Complexity）：{state.overall_final_scores.complexity}</div>
                  <div>规则评估综合得分：{state.overall_score}</div>
                </pre>
              )}
            </div>
          </div>
        </Form.Item>
      </Form>
    </Layout>
  );
}

export default PromptPage;
