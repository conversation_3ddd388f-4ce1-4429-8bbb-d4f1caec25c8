/*
 * @Author: 赵晓莹 <EMAIL>
 * @Date: 2025-03-21 09:44:50
 * @LastEditors: 赵晓莹 <EMAIL>
 * @LastEditTime: 2025-03-21 10:16:22
 * @FilePath: /seres-ai-nexus-pro/src/app/[locale]/prompt/expert/page.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
'use client';
import Layout from '@/components/Layout';
import { completions, propmt, queryDetail } from '@/services/prompt/api';
import useStore from '@/store/useStore';
import { PlusOutlined, StarTwoTone } from '@ant-design/icons';
import { Button, Card, Flex, Form, Input, Space, Typography, message } from 'antd';
import { useEffect, useState } from 'react';
import StyleSheet from '../page.module.less';
import TemplateSelectModal, { type Template } from './components/TemplateSelectModal';

// import { Template } from "./components/TemplateSelectModal";
const { Title } = Typography;
const { TextArea } = Input;

export default function PromptExpertPage() {
  const [question, setQuestion] = useState('');
  const [template, setTemplate] = useState<Partial<Template>>({
    content: '',
    name: '',
    id: 0,
  });
  useEffect(() => {
    queryDetail({ id: 4 }).then((res) => {
      setTemplate(res.resp[0]);
    });
  }, []);
  // optimize
  // ASSESSMENT
  // CONTRAST
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { userInfo } = useStore();
  const handleGenerate = async () => {
    if (!question || !template.name) {
      message.error('请填写问题并选择模板');
      return;
    }
    try {
      setLoading(true);
      const obj = {
        conversationId: 'conversationId', // 考虑从配置文件中读取
        messages: [
          {
            question: 'question',
            role: 'user', // 固定角色名，考虑常量化
            content: question,
            id: String(template.id),
          },
        ],
        userId: userInfo.id.toString(),
        stream: false,
      };
      const result = await propmt(obj);
      if (result.success && result.resp.length > 0) {
        setGeneratedPrompt(result.resp[0]?.choices[0]?.message?.content);
      } else {
        message.error(result.message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = () => {
    if (generatedPrompt) {
      navigator.clipboard.writeText(generatedPrompt);
      message.success('提示词已复制到剪贴板');
    } else {
      message.error('没有生成的提示词可供复制');
    }
  };
  const handleCompletions = async () => {
    const { resp, success } = await completions({
      input: question,
      output: generatedPrompt,
    });
    if (success) {
      message.success('收藏成功');
    } else {
      message.error('没有生成的提示词收藏');
    }
  };

  const handleSelectTemplate = (selectedTemplate: Template) => {
    setTemplate(selectedTemplate);
    setModalOpen(false);
  };

  return (
    <Layout curActive="/prompt/expert">
      <div className="p-6">
        <Title level={2} style={{ marginBottom: 24 }}>
          提示词专家
        </Title>
        <Card bordered={false}>
          <Form layout="vertical">
            <Form.Item label="输入问题" style={{ marginBottom: 16 }}>
              <Input
                placeholder="请输入您的问题"
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                style={{ width: '100%' }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 16 }}>
              <Flex gap="middle" align="flex-start" justify="space-between">
                <div className={StyleSheet.cardList}>
                  <div className={StyleSheet.cardListItem}>
                    <div className={StyleSheet.cardListItemTitleLine}>
                      <span className={StyleSheet.cardListItemTitle}>{template.name}</span>
                    </div>
                    <div className={StyleSheet.cardListItemLable}>{template.type}</div>
                    <div className={StyleSheet.cardListItemDesc}>{template.content}</div>
                  </div>
                </div>
                <Space>
                  <Button type="primary" icon={<PlusOutlined />} onClick={() => setModalOpen(true)}>
                    自定义模板
                  </Button>
                </Space>
              </Flex>
            </Form.Item>
            <Form.Item style={{ marginBottom: 16, marginTop: 32 }}>
              <Button type="primary" onClick={handleGenerate} loading={loading}>
                生成提示词
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 16 }}>
              <TextArea value={generatedPrompt} readOnly rows={10} />
            </Form.Item>
            <Flex align="flex-end" justify="space-between">
              <Button onClick={handleCopy} disabled={!generatedPrompt}>
                复制
              </Button>
              <Button onClick={handleCompletions} icon={<StarTwoTone />} disabled={!generatedPrompt}>
                收藏
              </Button>
            </Flex>
          </Form>
        </Card>

        {modalOpen && (
          <TemplateSelectModal open={modalOpen} onCancel={() => setModalOpen(false)} onSelect={handleSelectTemplate} />
        )}
      </div>
    </Layout>
  );
}
