

.topbar {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 25px;

    .card {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 24px;
        background: #fff;
        border: 1px solid #ccc;
        border-radius: 6px;
       cursor: pointer;

        &:hover {
            box-shadow: 0 2px 8px #070c141f;
        }

        .lefticon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56px;
            height: 56px;
            background: #eef3fe;
            border-radius: 50%;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .rightcontent {
            flex: 1;
            margin-left: 24px;

            .righthead {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0;
                font-size: 16px;

                .rigthtitle {
                    color: #151b26;
                    font-weight: 500;
                    line-height: 24px;
                }

                // .righticon {
                //         cursor: pointer;
                // }
            }

            .rightbody {
                margin-top: 8px;
                color: #5c5f66;
                font-weight: 400;
                font-size: 12px;
                line-height: 22px;
            }
        }
    }
}
.EmptyCenter{
    width: 100%;
    display: flex;
    align-items: center;

}


.cardList {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(310px, 1fr));
    gap: 16px;
    box-sizing: border-box;
    margin-bottom: 16px;
    .cardListItem {
        box-sizing: border-box;
        padding: 16px;
        background: url('/images/templatebg-2f0af46e.png') right top / contain no-repeat, linear-gradient(to bottom, #f3f8ff, #fff 100%);
        background-color: #fff;
        border: 1px solid #e8e9eb;
        border-radius: 6px;
        // cursor: pointer;

        .cardListItemTitleLine {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 12px;
        }

        .cardListItemTitle {
            overflow: hidden;
            color: #151b26;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
              cursor: pointer;
            
        }

        .cardListItemExtra {
            display: flex;
            flex-direction: row;
            flex-shrink: 0;
            align-items: center;
            margin-left: auto;
            font-size: 16px;
            cursor: pointer;
        }
    }

    .cardListItemLable {
        display: inline-block;
        padding: 0 8px;
        color: rgb(128 14 25);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        background: rgb(255 232 230);
        border-radius: 2px;
    }

    .cardListItemDesc {
        display: -webkit-box;
        height: 44px;
        margin-top: 8px;
        margin-bottom: 12px;
        overflow: hidden;
        color: #5c5f66;
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
          cursor: pointer;
    }

}

.pagerbar {
    display: flex;
    justify-content: flex-end;
}

.panel {
    margin-top: 32px;
    overflow: hidden;
    border: 1px solid #d4d6d9;
    border-radius: 6px;

    .panelTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 15px;
        background: url('/images/result_cover.svg') no-repeat, linear-gradient(#e7efff, #fff);
    }

    .panelContent {
        margin-bottom: 15px;

        .panelContentItem {
            padding: 10px;
            background-color: #E8ECF0;
            border-bottom: 1px dashed #a8caff;
        }
    }

    .panelFooter {
        min-height: 200px;

    }
}

.assessment {
    display: grid;
    grid-auto-flow: row dense;
    grid-template: repeat(3, 1fr) / repeat(2, 1fr);
    gap: 5px;
    padding: 10px;
    border-top: 1px dashed #a8caff;

    &>div {
        // text-align: center;text-align
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        background: url('/images/result_cover.svg') no-repeat, linear-gradient(#e7efff, #fff);
        border: 1px solid #cfcfce;

        pre {
        //   margin-left: 20%;
        //   transform: translateX(-20%);
        width: 400px;
            div {
                font-size: 14px;
                font-family: monospace;
                line-height: 2;
                white-space: pre;
                unicode-bidi: isolate;
            }
        }
    }

    .assessmentDiv1 {
        grid-row: 1 / 4;
        grid-column: 1 / 2;
        // place-content: center / center;
        // place-items: center / center;
        // display: flex;
        // align-items: center;
        // justify-content: center;

    }
}

.contrast {
    display: grid;
    // grid-template: repeat(3, 1fr) / repeat(2, 1fr);grid-template
    grid-template-rows: 1fr 60px 1fr;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
    padding: 10px;
    border-top: 1px dashed #a8caff;

    &>div {
        background: url('/images/result_cover.svg') no-repeat, linear-gradient(#e7efff, #fff);
        border: 1px solid #cfcfce;
    }

    .contrastDiv1,
    .contrastDiv2 {
        padding: 10px 5px;
    }

    .contrastDiv3 {
        display: flex;
        grid-row: 2 / 3;
        grid-column: 1 / 3;
        align-items: center;
        justify-content: flex-end;
        padding: 0 15px;
    }

    .contrastDiv4 {
        grid-row: 3 / 4;
        grid-column: 1 / 3;
    }
}
