export interface CreateParams {
  name: string;
  content?: string;
}

export interface UpdateParams extends CreateParams {
  id: number;
}

export interface KonwListParams {
  params: {
    name?: string; // 修改为可选参数
  };
  current: number;
  pageSize: number;
}

export interface KonwDetailParams {
  params: {
    name?: string; // 修改为可选参数
    knowId?: string;
  };
  current: number;
  pageSize: number;
}
