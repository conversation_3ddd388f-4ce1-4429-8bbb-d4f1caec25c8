.variableModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-header {
      margin-bottom: 16px;
      border-bottom: 1px solid #e6f7ff;
    }

    .ant-modal-body {
      padding: 16px 24px;
    }
  }
}

.variableTable {
  width: 100%;
}

.tableHeader {
  display: flex;
  background-color: #f5f5f5;
  padding: 12px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-weight: 500;
}

.tableRow {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.nameColumn {
  flex: 1;
  padding: 0 8px;
}

.descColumn {
  flex: 1.5;
  padding: 0 8px;
}

.valueColumn {
  flex: 1.5;
  padding: 0 8px;
}

.actionColumn {
  width: 60px;
  text-align: center;
  padding: 0 8px;
}

.required {
  color: #ff4d4f;
  margin-left: 4px;
}

.addButton {
  margin-top: 16px;
  color: #1677ff;
  padding-left: 0;
}

.emptyVariables {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
  color: #8c8c8c;
  text-align: center;
  height: 120px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  margin: 16px 0;
}