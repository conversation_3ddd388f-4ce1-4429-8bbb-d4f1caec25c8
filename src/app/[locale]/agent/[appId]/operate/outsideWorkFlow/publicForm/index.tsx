'use client';
import { memo, forwardRef, useImperativeHandle } from 'react';
import { Form } from 'antd';
import FormItem from './formItem';
import { FormFieldItem } from './types';

interface IProps {
  parameters: Array<any>; // 原始数据可能有不同的字段名，所以使用 any 类型
  url: string;
}

export interface PublicFormRef {
  validateFields: () => Promise<any>;
}
const PublicForm = forwardRef<PublicFormRef, IProps>(({ parameters, url }, ref) => {
  const [form] = Form.useForm();

  // 转换嵌套结构为扁平数组
  const formattedParameters: FormFieldItem[] = Array.isArray(parameters)
    ? parameters.map((item: any) => Object.values(item)[0] as FormFieldItem)
    : [];

  // 暴露表单方法给父组件
  useImperativeHandle(ref, () => ({
    validateFields: () => form.validateFields(),
  }));

  return (
    <Form form={form} layout="vertical" autoComplete="off">
      {formattedParameters.map((item, index) => {
        return <FormItem key={index} item={item} url={url} />;
      })}
    </Form>
  );
});

PublicForm.displayName = 'PublicForm';

export default memo(PublicForm);
