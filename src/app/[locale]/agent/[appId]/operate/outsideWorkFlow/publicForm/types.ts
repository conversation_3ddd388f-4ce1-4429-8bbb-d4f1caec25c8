// 定义表单字段的数据结构
export interface FormFieldItem {
  type: string;
  variable: string;
  label: string;
  required?: boolean;
  default?: any;
  options?: Array<string> | Array<{ label: string; value: any }>;
  placeholder?: string;
  description?: string;
  max_length?: number;
  allowed_file_upload_methods?: string[];
  allowed_file_types?: string[];
  allowed_file_extensions?: string[];
}

// 表单输入类型枚举
export enum InputVarType {
  textInput = 'text-input',
  paragraph = 'paragraph',
  select = 'select',
  number = 'number',
  // url = 'url',
  files = 'files',
  // json = 'json', // obj, array
  // contexts = 'contexts', // knowledge retrieval
  // iterator = 'iterator', // iteration input
  singleFile = 'file',
  // multiFiles = 'file-list',
  // loop = 'loop', // loop input
}

export enum fileType {
  document = '.TXT,.MD,.MDX,.MARKDOWN,.PDF,.HTML,.XLSX,.XLS,.DOCX,.CSV,.EML,.MSG,.PPTX,.PPT,.XML,.EPUB',
  image = '.JPG,.JPEG,.PNG,.GIF,.BMP,.WEBP,.SVG',
  audio = '.MP3,.WAV,.M4A,.WEBM,.AMR',
  video = '.MP4,.MOV,.MPEG,.MPEA',
}
