'use client';
import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, Select, Space, message, Descriptions } from 'antd';
import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import { updateTool } from '@/services/tool/api';

const { TextArea } = Input;
const { Option } = Select;

interface ToolDetail {
  id: string;
  name: string;
  description: string;
  basePath: string;
  requestMethod: string;
  baseUrl?: string;
}

interface BasicInfoCardProps {
  toolDetail: ToolDetail;
  onUpdate: () => void;
}

const REQUEST_METHOD_OPTIONS = [
  {
    label: 'GET',
    value: 'GET',
  },
  {
    label: 'POST',
    value: 'POST',
  },
  {
    label: 'PUT',
    value: 'PUT',
  },
  {
    label: 'DELETE',
    value: 'DELETE',
  },
  {
    label: 'PATCH',
    value: 'PATCH',
  },
];

const BasicInfoCard: React.FC<BasicInfoCardProps> = ({ toolDetail, onUpdate }) => {
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();

  // 设置表单初始值
  useEffect(() => {
    if (toolDetail) {
      form.setFieldsValue({
        name: toolDetail.name,
        description: toolDetail.description,
        basePath: toolDetail.basePath,
        requestMethod: toolDetail.requestMethod,
      });
    }
  }, [toolDetail, form]);

  // 保存基本信息
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const updateData = {
        id: toolDetail.id,
        ...values,
      };

      const { errorCode } = await updateTool(updateData);

      if (errorCode === 0) {
        message.success('保存成功');
        setEditing(false);
        onUpdate(); // 通知父组件更新数据
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleEdit = () => {
    // 先设置表单值，再切换编辑状态
    form.setFieldsValue({
      name: toolDetail.name,
      description: toolDetail.description,
      basePath: toolDetail.basePath,
      requestMethod: toolDetail.requestMethod,
    });
    setEditing(true);
  };

  const handleCancel = () => {
    setEditing(false);
    // 重置表单到原始值
    form.setFieldsValue({
      name: toolDetail.name,
      description: toolDetail.description,
      basePath: toolDetail.basePath,
      requestMethod: toolDetail.requestMethod,
    });
  };

  return (
    <Card
      title="基本信息"
      variant="borderless"
      extra={
        <Space>
          {editing ? (
            <>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
                保存
              </Button>
            </>
          ) : (
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              编辑
            </Button>
          )}
        </Space>
      }
      style={{ marginBottom: '24px' }}
      styles={{
        header: {
          background: '#d6ebff',
          borderRadius: 0,
        },
        body: {
          background: '#f1f8ff',
          borderRadius: 0,
        },
      }}
    >
      {editing ? (
        <Form form={form} layout="vertical">
          <Form.Item label="工具名称" name="name" rules={[{ required: true, message: '请输入工具名称' }]}>
            <Input placeholder="请输入工具名称" />
          </Form.Item>
          <Form.Item label="工具描述" name="description" rules={[{ required: true, message: '请输入工具描述' }]}>
            <TextArea rows={3} placeholder="请输入工具描述" />
          </Form.Item>
          <Form.Item label="工具路径" name="basePath" rules={[{ required: true, message: '请输入工具路径' }]}>
            <Input addonBefore={toolDetail.baseUrl} placeholder="请输入工具路径" />
          </Form.Item>
          <Form.Item label="请求方法" name="requestMethod" rules={[{ required: true, message: '请选择请求方法' }]}>
            <Select placeholder="请选择请求方法">
              {REQUEST_METHOD_OPTIONS.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      ) : (
        <Descriptions column={1}>
          <Descriptions.Item label="工具名称">{toolDetail.name}</Descriptions.Item>
          <Descriptions.Item label="工具描述">{toolDetail.description}</Descriptions.Item>
          <Descriptions.Item label="工具路径">{toolDetail.baseUrl + (toolDetail.basePath ?? '')}</Descriptions.Item>
          <Descriptions.Item label="请求方法">{toolDetail.requestMethod}</Descriptions.Item>
        </Descriptions>
      )}
    </Card>
  );
};

export default BasicInfoCard;
