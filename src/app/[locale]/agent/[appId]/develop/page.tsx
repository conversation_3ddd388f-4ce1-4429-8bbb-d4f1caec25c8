'use client';
import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, message } from 'antd';
import Layout from '@/components/LayoutAgent';
import { useParams } from 'next/navigation';
import { getAgentApi, agentDetail } from '@/services/agent/api';
import styles from './page.module.less';

const DevelopPage: React.FC = () => {
  const agentId = useParams().appId;
  const [chatUrl, setChatUrl] = useState<string>('');
  const [detail, setDetail] = useState<any>({});

  //agent 获取agent URL
  const getApi = async () => {
    const res = await getAgentApi(Number(agentId));
    if (res.success) {
      setChatUrl(res.resp[0].url);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //获取智能体详情
  const getDetail = async () => {
    const res = await agentDetail(Number(agentId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    getApi();
    getDetail();
  }, []);

  return (
    <Layout curActive={`/agent/${agentId}/develop`} detailData={detail}>
      <h3>后端服务API</h3>
      {/* <h4>URL</h4> */}
      <Card className={styles.apiCard} styles={{ body: { padding: 0 } }}>
        <div className={styles.apiCardHeader}>URL</div>
        <div className={styles.apiCardContent}>
          <span className={styles.apiUrl}>{chatUrl}</span>
          <Button
            type="text"
            className={styles.copyBtn}
            onClick={() => {
              navigator.clipboard.writeText(chatUrl);
              message.success('已复制');
            }}
          >
            Copy
          </Button>
        </div>
      </Card>
    </Layout>
  );
};

export default DevelopPage;
