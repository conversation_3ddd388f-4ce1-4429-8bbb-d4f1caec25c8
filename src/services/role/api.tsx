import req from '@/utils/request';
import { CreateRoleData, UpdateRoleData, assignUserData, deleteUserData } from '@/types/role/index';

//创建角色
export const createRole = (data: CreateRoleData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/role/create', { ...data });

//编辑角色
export const editRole = (data: UpdateRoleData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/role/update', { ...data });

//删除角色
export const deleteRole = (id: number): Promise<SERES_RESPONSE> => req.post(`/ai-manage/v1/role/delete/${id}`);
//查询角色详情
export const getRoleInfo = (id: number): Promise<SERES_RESPONSE> => req.get(`/ai-manage/v1/role/detail/${id}`);

//给角色分配用户
export const assignUser = (data: assignUserData): Promise<SERES_RESPONSE> =>
  req.post(`/ai-manage/v1/role/assignUser`, { ...data });

//删除给角色分配的用户
export const deleteUser = (data: deleteUserData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/role/deleteUser', { ...data });
