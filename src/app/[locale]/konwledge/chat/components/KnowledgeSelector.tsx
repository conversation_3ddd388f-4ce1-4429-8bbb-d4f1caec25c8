import React from 'react';
import { Select, Space, Tag } from 'antd';
import { BookOutlined } from '@ant-design/icons';

// 知识库数据类型
export type KnowledgeBase = {
  id: number;
  name: string;
  content?: string;
  documentNum?: string;
  createTime?: string;
};

export interface KnowledgeSelectorProps {
  knowledgeBases: KnowledgeBase[];
  selectedKnowledgeBases: number[];
  onKnowledgeBasesChange: (selectedIds: number[]) => void;
  loading?: boolean;
  placeholder?: string;
  style?: React.CSSProperties;
}

const KnowledgeSelector: React.FC<KnowledgeSelectorProps> = ({
  knowledgeBases,
  selectedKnowledgeBases,
  onKnowledgeBasesChange,
  loading = false,
  placeholder = '请选择要使用的知识库',
  style,
}) => {
  if (knowledgeBases.length === 0) {
    return null;
  }

  return (
    <div style={{ padding: '8px 0', borderTop: '1px solid #f0f0f0', ...style }}>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BookOutlined style={{ color: '#1677ff' }} />
          <span style={{ fontSize: '12px', color: '#666' }}>选择知识库：</span>
        </div>
        <Select
          mode="multiple"
          placeholder={placeholder}
          value={selectedKnowledgeBases}
          onChange={onKnowledgeBasesChange}
          style={{ width: '100%' }}
          size="small"
          maxTagCount="responsive"
          loading={loading}
          options={knowledgeBases.map((kb) => ({
            label: kb.name,
            value: kb.id,
          }))}
          tagRender={(props) => <Tag {...props} color="blue" style={{ margin: '2px' }} icon={<BookOutlined />} />}
        />
        {selectedKnowledgeBases.length > 0 && (
          <div style={{ fontSize: '11px', color: '#999' }}>已选择 {selectedKnowledgeBases.length} 个知识库</div>
        )}
      </Space>
    </div>
  );
};

export default KnowledgeSelector;
