.canvasWrap {
    position: relative;
    width: 100%;
    height: 100%;
    border: 1px dashed #264deb;
    border-radius: 6px;

    .canvas {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .item {
            position: absolute;
            display: inline-block;
            width: 40px;
            height: 40px;
            // background: #eee;
            color: #333;
            line-height: 40px;
            text-align: center;
            border-radius: 5px;

            img {
                width: 100%;
            }
        }

        :global(#cube-holder-block) {
            position: absolute;
            display: none;
            width: 100px;
            height: 100px;
            border: 2px dashed rgb(14 105 241);
            border-radius: 6px;
            transition: all .1s;
            pointer-events: none;
        }
    }
}

.toolBar {
    position: relative;
    z-index: 100;
}

.sizeWrap {
    .defaultWrap {
        display: flex;
        flex-wrap: wrap;

        .item {
            margin: 10px;
            text-align: center;

            .icon {
                display: block;
                width: 80px;
                height: 52px;
                margin-bottom: 2px;
                line-height: 52px;
                border: 1px solid #eee;
                border-radius: 6px;
                cursor: pointer;

                & + span {
                    font-size: 12px;
                }

                &:hover {
                    border-color: #264deb;

                    & + span {
                        color: #264deb;
                    }
                }
            }
        }
    }

    .customWrap {
        
    }
}