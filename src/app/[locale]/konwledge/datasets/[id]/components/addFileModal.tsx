'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, message, Upload } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { uploadFileReq } from '@/services/konwledge/api';

const { Dragger } = Upload;

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  id: string;
}

const AddFileModal: React.FC<IProps> = ({ open, onCancel, onSuccess, id }) => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [files, setFiles] = useState<UploadFile[]>([]);
  console.log('leicj', id);

  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);
      const formData = new FormData();
      formData.append('knowId', id);
      files.length > 0 &&
        files.map((file) => {
          formData.append('file', file as any);
        });
      uploadFileReq(formData).then((res) => {
        if (res.success) {
          message.success('上传成功');
          onSuccess();
        } else {
          message.error(res.msg || '上传失败');
        }
      });
    } catch (error) {
      message.error('保存失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open) {
      setFiles([]);
    }
  }, [open]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    setFiles([]);
    onCancel();
  };

  const props: UploadProps = {
    name: 'file',
    maxCount: 1,
    // multiple: true,
    beforeUpload: (file, fileList) => {
      // setFiles([...files, ...fileList] as UploadFile[]);
      setFiles([...fileList] as UploadFile[]);
      return false;
    },
    onChange(info) {},
    onDrop(e) {},
    onRemove(file) {
      setFiles((prevFiles) => (prevFiles || []).filter((prevFile) => prevFile.uid !== file.uid));
    },
  };

  return (
    <Modal
      title="上传文件"
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitLoading}
          onClick={handleSubmit}
          disabled={files.length === 0}
        >
          确定
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <Dragger {...props}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件至此区域即可上传</p>
        <p className="ant-upload-hint">支持单次上传。严禁上传公司数据或其他违禁文件。</p>
      </Dragger>
    </Modal>
  );
};

export default AddFileModal;
