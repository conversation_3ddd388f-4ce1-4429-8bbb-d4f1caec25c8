'use client';
import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button, message, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import { getToolDetail } from '@/services/tool/api';
import BasicInfoCard from './components/BasicInfoCard';
import ParameterCard from './components/ParameterCard';

interface ToolDetail {
  id: string;
  name: string;
  description: string;
  basePath: string;
  requestMethod: string;
  inputData?: ParameterItem[];
  outputData?: ParameterItem[];
  baseUrl?: string;
}

interface ParameterItem {
  name: string;
  description: string;
  type: string;
  required: boolean;
  enabled: boolean;
  key?: string;
  method?: string;
  defaultValue?: string;
}

const ToolEditPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const toolId = searchParams.get('id');

  const [loading, setLoading] = useState(false);
  const [toolDetail, setToolDetail] = useState<ToolDetail | null>(null);

  // 返回上一页
  const handleGoBack = () => {
    router.back();
  };

  // 获取工具详情
  const fetchToolDetail = async () => {
    if (!toolId) return;

    try {
      setLoading(true);
      const { data, errorCode } = await getToolDetail(toolId);

      if (errorCode === 0 && data) {
        const _data = data.data;
        _data.inputData = _data.inputData ? JSON.parse(_data.inputData) : [];
        _data.outputData = _data.outputData ? JSON.parse(_data.outputData) : [];
        _data.baseUrl = data.aiPlugin.baseUrl || '';
        setToolDetail(_data);
      } else {
        message.error('获取工具详情失败');
      }
    } catch (error) {
      message.error('获取工具详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchToolDetail();
  }, [toolId]);

  if (loading) {
    return (
      <Layout curActive="/llmsTool/plugin">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  if (!toolDetail) {
    return (
      <Layout curActive="/llmsTool/plugin">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>工具详情不存在</p>
          <Button onClick={handleGoBack}>返回</Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout curActive="/llmsTool/plugin">
      <div style={{ padding: '10px' }}>
        {/* 返回按钮 */}
        <div style={{ marginBottom: '24px' }}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
            返回
          </Button>
        </div>

        {/* 基本信息 */}
        <BasicInfoCard toolDetail={toolDetail} onUpdate={fetchToolDetail} />

        {/* 配置输入参数 */}
        <ParameterCard
          title="配置输入参数"
          toolId={toolDetail.id}
          parameters={toolDetail.inputData || []}
          fieldName="inputData"
          onUpdate={fetchToolDetail}
          type="input"
        />

        {/* 配置输出参数 */}
        <ParameterCard
          title="配置输出参数"
          toolId={toolDetail.id}
          parameters={toolDetail.outputData || []}
          fieldName="outputData"
          onUpdate={fetchToolDetail}
          type="output"
        />
      </div>
    </Layout>
  );
};

export default ToolEditPage;
