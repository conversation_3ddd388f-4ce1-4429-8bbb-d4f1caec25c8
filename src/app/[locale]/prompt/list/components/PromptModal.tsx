'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Select, message } from 'antd';
import { create, update, queryDetail } from '@/services/prompt/api';
const { TextArea } = Input;

const templateTypes = [
  { label: '公有', value: '公有' },
  { label: '私有', value: '私有' },
];

interface PromptModalProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<PromptTemplate>;
  mode?: 'add' | 'edit' | 'view';
}

interface PromptTemplate {
  id: number;
  name: string;
  content: string;
  type: string;
}

const PromptModal: React.FC<PromptModalProps> = ({ open, onCancel, onSuccess, initialValues, mode = 'add' }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const isView = mode === 'view';
  const getCaseExample = (flag: boolean) => {
    const htmlContent = `
    <b style="color: #4096ff;">角色：</b>汽车配置分析师，
    <b style="color: #4096ff;">背景：</b>用户希望了解奔驰E300的具体配置情况，包括性能、舒适性、安全性和价格等方面的信息，以便做出购车决策。
    <b style="color: #4096ff;">目标:</b>
    为用户提供奔驰E300的详细配置信息，包括动力系统、内饰配置、安全性能等，帮助用户全面了解该车型的特点和优势，以便做出明智的购车决策。
    <b style="color: #4096ff;">要求:</b>
    1. 收集奔驰E300的官方配置信息，包括动力系统、内饰配置、安全性能等。
    2. 对比同级别车型的配置，突出奔驰E300的优势和特点。
  `;
    return flag ? (
      <div style={{ whiteSpace: 'pre-wrap', padding: '10px' }} dangerouslySetInnerHTML={{ __html: htmlContent }} />
    ) : null;
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const { success, msg } =
        mode === 'edit' ? await update({ ...values, id: initialValues?.id }) : await create(values);
      if (success) {
        // TODO: 实现保存逻辑
        message.success(msg);
        onSuccess();
        setSubmitLoading(true);
      }
    } catch (error: any) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('保存失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [open, initialValues, form]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={mode === 'add' ? '新增模板' : mode === 'edit' ? '编辑模板' : '查看模板'}
      open={open}
      onCancel={handleCancel}
      footer={
        isView
          ? [
              <Button key="cancel" onClick={handleCancel}>
                关闭
              </Button>,
            ]
          : [
              <Button key="cancel" onClick={handleCancel}>
                取消
              </Button>,
              <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
                保存
              </Button>,
            ]
      }
      width={800}
      destroyOnClose
    >
      <Form form={form} layout="vertical" disabled={isView}>
        <Form.Item label="模板名称" name="name" rules={[{ required: true, message: '请输入模板名称' }]}>
          <Input placeholder="请输入模板名称" />
        </Form.Item>

        <Form.Item label="模板类型" name="type" rules={[{ required: true, message: '请选择模板类型' }]}>
          <Select options={templateTypes} placeholder="请选择模板类型" />
        </Form.Item>

        <Form.Item
          label="模板内容"
          name="content"
          rules={[
            { required: true, message: '请输入模板内容' },
            {
              validator: (_, value) => {
                if (value.length > 1000) {
                  return Promise.reject(new Error('不能1000字符'));
                }
                return Promise.resolve();
              },
            },
          ]}
          extra={getCaseExample(!isView)}
        >
          <TextArea placeholder="请输入模板内容" rows={6} showCount={!isView} maxLength={1000} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PromptModal;
