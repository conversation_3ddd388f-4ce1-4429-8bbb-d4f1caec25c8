'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, message } from 'antd';
import { saveTool } from '@/services/tool/api';

const { TextArea } = Input;

interface AddModalProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  id: string;
}

const AddToolModal: React.FC<AddModalProps> = ({ open, onCancel, onSuccess, id }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 当弹窗打开时，如果是编辑模式，填充表单数据
  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open, form]);

  // 提交表单
  const handleOk = async () => {
    const values = await form.validateFields();
    setLoading(true);

    const submitData = {
      ...values,
      pluginId: id,
    };

    const result = await saveTool(submitData);

    if (result.errorCode === 0) {
      message.success('新增成功');
      onSuccess();
      onCancel();
    } else {
      message.error(result.errorMessage || '新增失败');
    }
    setLoading(false);
  };

  return (
    <Modal
      title={'创建工具'}
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      width={700}
      okText="确定"
      cancelText="取消"
      confirmLoading={loading}
      styles={{
        body: {
          maxHeight: '75vh',
          overflowY: 'auto',
          paddingBottom: 15,
        },
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '16px',
        },
      }}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 19 }}
        style={{ marginTop: '24px' }}
      >
        {/* 工具名称 */}
        <Form.Item label="工具名称" name="name" rules={[{ required: true, message: '请输入工具名称' }]}>
          <Input placeholder="请输入工具名称" maxLength={40} showCount allowClear />
        </Form.Item>

        {/* 工具描述 */}
        <Form.Item label="描述" name="description" rules={[{ required: true, message: '请输入工具描述' }]}>
          <TextArea placeholder="请输入描述描述" rows={4} maxLength={500} showCount allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddToolModal;
