'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message, Image } from 'antd';
import { createMark, updateMark } from '@/services/mark/api';
import { CreateParams, UpdateParams } from '@/types/mark/index';
import { BsRobot } from 'react-icons/bs';
import styles from './index.module.less';

const { TextArea } = Input;

interface ParamsType {
  id?: number;
  questions: string;
  answers: string;
}

interface IProps {
  agentId: string;
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<ParamsType> | undefined;
  mode?: 'add' | 'edit' | 'see';
}

const MODE_TITLE = {
  add: '添加标注',
  edit: '编辑标注',
  see: '查看标注',
};

const AddOrEditMark: React.FC<IProps> = ({ agentId, open, onCancel, onSuccess, initialValues, mode = 'add' }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      form.validateFields().then(async (values) => {
        if (!values.questions) {
          message.error('请输入问题');
          return;
        }
        if (!values.answers) {
          message.error('请输入答案');
          return;
        }

        setSubmitLoading(true);
        if (mode === 'add') {
          createHandle(values);
        } else {
          // 编辑模式
          updateHandle({ id: initialValues?.id, ...values });
        }
      });
    } catch (error) {
      message.error('保存失败');
    }
  };

  const createHandle = (data: CreateParams) => {
    createMark({ ...data, agentId: Number(agentId) }).then((res) => {
      if (res.success) {
        message.success('添加成功');
        setSubmitLoading(false);
        onSuccess();
      } else {
        message.error(res.msg || '添加失败');
      }
    });
  };

  const updateHandle = (data: UpdateParams) => {
    updateMark({ ...data, agentId: Number(agentId) }).then((res) => {
      if (res.success) {
        message.success('编辑成功');
        setSubmitLoading(false);
        onSuccess();
      } else {
        message.error(res.msg || '编辑失败');
      }
    });
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue(initialValues);
    }
    if (open && !initialValues) {
      form.resetFields();
    }
  }, [open, initialValues]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title={MODE_TITLE[mode]}
      open={open}
      onCancel={handleCancel}
      footer={
        mode === 'see'
          ? []
          : [
              <Button key="cancel" onClick={handleCancel}>
                取消
              </Button>,
              <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
                确定
              </Button>,
            ]
      }
      width={800}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <div className={styles.block}>
          <div className={styles.questionsIcon}>
            <Image src="/images/user.svg" alt="user" width={30} />
          </div>
          <Form.Item label="提问" name="questions">
            <TextArea placeholder="输入问题" rows={6} maxLength={500} disabled={mode === 'see'} />
          </Form.Item>
        </div>

        <div className={styles.block}>
          <div className={styles.answersIcon}>
            <BsRobot />
          </div>
          <Form.Item label="回复" name="answers">
            <TextArea placeholder="输入回复" rows={6} maxLength={500} disabled={mode === 'see'} />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};

export default AddOrEditMark;
