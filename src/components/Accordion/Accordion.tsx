import React from 'react';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import { Avatar, List } from 'antd';
import styles from './accordion.module.less';

/**
 * 手风琴面板项
 */
export type AccordionItemType = {
  icon?: string;
  title: string;
  description: string;
  children?: React.ReactNode;
  isActive?: boolean | false;
  clickItem: () => void;
};
const AccordionItem: React.FC<AccordionItemType> = ({ icon, title, description, children, isActive, clickItem }) => {
  return (
    <div className={`${styles.accordionItem} ${isActive ? styles.active : ''}`}>
      <div className={styles.accordionHeader} onClick={clickItem}>
        <List>
          <List.Item>
            <List.Item.Meta avatar={<Avatar src={icon || '/favicon.png'} />} title={title} description={description} />
          </List.Item>
        </List>
        <span className={styles.accordionIcon}>
          {isActive ? <DownOutlined style={{ fontSize: '12px' }} /> : <RightOutlined style={{ fontSize: '12px' }} />}
        </span>
      </div>
      {isActive && <div className={styles.accordionContent}>{children}</div>}
    </div>
  );
};

export { AccordionItem };
