'use client';
import Layout from '@/components/Layout';
import useStore from '@/store/useStore';
import { Button, Input, message, Typography } from 'antd';
import { useState } from 'react';
import StyleSheet from '../page.module.less';
const { Title } = Typography;
const { TextArea } = Input;

import { getPromptCompare } from '@/services/prompt/api';

function PromptPage() {
  const [state, setState] = useState({
    id: '',
    content: '',
    aftercontent: '',
    best_prompt: '',
  });
  const [loading, setLoading] = useState(false);
  const { userInfo } = useStore();
  const handleGenerate = async () => {
    if (!state.content && !state.aftercontent) {
      message.error('请填写提示词');
      return;
    }
    try {
      setLoading(true);
      const obj = {
        conversationId: 'conversationId', // 考虑从配置文件中读取
        messages: [{ prompt: state.content }, { prompt: state.aftercontent }],
        userId: userInfo.id.toString(),
        stream: false,
      };
      const result = await getPromptCompare(obj);
      if (result.success && result.resp.length > 0) {
        const { best_prompt } = result.resp[0];
        setState((pre) => ({
          ...pre,
          ...{
            best_prompt,
          },
        }));
      } else {
        message.error(result.message || '查询失败');
      }
    } catch (error) {
      message.error('查询失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Layout curActive="/prompt/contrast">
      <Title level={2} style={{ marginBottom: 24 }}>
        提示词对比
      </Title>
      <div className={StyleSheet.contrast}>
        <div className={StyleSheet.contrastDiv1}>
          <TextArea
            disabled={loading}
            value={state.content}
            rows={10}
            placeholder="请输入提示词"
            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
              setState((prev) => ({ ...prev, content: e.target.value }))
            }
          />
        </div>
        <div className={StyleSheet.contrastDiv2}>
          <TextArea
            disabled={loading}
            value={state.aftercontent}
            rows={10}
            placeholder="请输入提示词"
            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
              setState((prev) => ({ ...prev, aftercontent: e.target.value }))
            }
          />
        </div>
        <div className={StyleSheet.contrastDiv3}>
          <Button
            type="primary"
            onClick={handleGenerate}
            loading={loading}
            disabled={!state.content && !state.aftercontent}
          >
            对比
          </Button>
        </div>
        <div className={StyleSheet.contrastDiv4}>
          <TextArea value={state.best_prompt} rows={10} placeholder="最优提示词" />
        </div>
      </div>
    </Layout>
  );
}

export default PromptPage;
