'use client';
import React, { useEffect, useState, memo, useRef } from 'react';
import { Card, Button, Spin, message } from 'antd';
import { SettingOutlined, SendOutlined } from '@ant-design/icons';
import { getWorkflowVar, runWorkflow } from '@/services/requestDify/api';
import PublicForm, { PublicFormRef } from './publicForm';
import ReactJsonView from '@/components/JsonView';
import Cookies from 'js-cookie';
import styles from './index.module.less';

interface IProps {
  url: string;
}
const OutsideFlow: React.FC<IProps> = ({ url }) => {
  const [parameters, setParameters] = useState([]);
  const [executeResult, setExecuteResult] = useState({});
  const [loading, setLoading] = useState(false);
  const formRef = useRef<PublicFormRef>(null);

  //获取变量
  const getVar = async () => {
    const res = await getWorkflowVar(url);
    setParameters(res.user_input_form);
  };

  // 提取文件ID
  const extractFileId = (fileData: any): string | null => {
    if (!fileData) return null;
    return (
      fileData.file?.response?.id ||
      fileData.fileList?.[0]?.response?.id ||
      fileData.response?.id ||
      (typeof fileData === 'string' ? fileData : null)
    );
  };

  // 处理文件输入字段
  const processFileInputs = (formValues: any) => {
    const processedInputs = { ...formValues };
    const formattedParameters = Array.isArray(parameters)
      ? parameters.map((item: any) => Object.values(item)[0] as any)
      : [];

    formattedParameters.forEach((param: any) => {
      if (param.type === 'file' && formValues[param.variable]) {
        const uploadFileId = extractFileId(formValues[param.variable]);
        if (uploadFileId) {
          processedInputs[param.variable] = {
            transfer_method: param.allowed_file_upload_methods?.[0] || 'local_file',
            type: param.allowed_file_types?.[0] || 'document',
            upload_file_id: uploadFileId,
            url: '',
          };
        }
      }
    });

    return processedInputs;
  };

  //运行工作流
  const run = async () => {
    formRef.current?.validateFields().then(async (values) => {
      try {
        setLoading(true);
        setExecuteResult({});
        const inputs = processFileInputs(values);
        const res = await runWorkflow(url, {
          user: Cookies.get('access_token'),
          // files: [],
          inputs,
        });
        setExecuteResult(res);
      } catch (e) {
        message.error('请求失败');
      } finally {
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    url && getVar();
  }, [url]);
  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <SettingOutlined className={styles.titleIcon} />
        <span className={styles.titleText}>智能体调试</span>
      </div>

      <div className={styles.content}>
        <Card title="请输入参数" className={styles.inputCard}>
          <PublicForm ref={formRef} parameters={parameters} url={url} />
          <Button type="primary" block onClick={run} loading={loading} className={styles.runButton}>
            <SendOutlined />
            开始运行
          </Button>
        </Card>

        <div className={styles.resultArea}>
          <Card title="结果" className={styles.resultCard}>
            <Spin spinning={loading} tip="正在运行中...">
              <div style={{ minHeight: '200px' }}>
                <ReactJsonView value={executeResult} />
              </div>
            </Spin>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default memo(OutsideFlow);
