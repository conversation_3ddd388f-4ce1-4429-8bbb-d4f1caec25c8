'use client';

import { DeleteOutlined, EditOutlined, PlusOutlined, MenuOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import Layout from '@/components/Layout';
import { AiProChat, ChatMessage } from './components/AiProChat';
import KnowledgeSelector, { KnowledgeBase } from './components/KnowledgeSelector';
// import { AiProChat, ChatMessage } from '@/components/AiProChat/AiProChat';
import { useSse } from '@/hooks/useSse';
import type { ConversationsProps } from '@ant-design/x';
import { Conversations } from '@ant-design/x';
import { Button, type GetProp, message, Input, theme, Modal } from 'antd';
import styles from './chat.module.less';
import {
  doGetConverManualDelete,
  doGetManual,
  getConversationManualGet,
  doGetConverManualUpdate,
  getList,
} from '@/services/konwledge/api';
import { getExternalSessionId, setNewExternalSessionId, updateExternalSessionId } from '@/utils/getExternalSessionId';
import { getSessionId } from '@/utils/getSessionId';
import { uuid } from '@/utils/uuid';
import React, { useEffect, useState, CSSProperties, useCallback } from 'react';
import useStore from '@/store/useStore';

import { userId } from '@/utils/storage';
export const outerStyle: CSSProperties = {
  margin: 0,
  padding: 0,
  height: 'calc(100vh - 66px)',
};
type IPropsConversationItem = {
  qa_id: string;
  user_id: string;
  bot_id: string;
  kb_id: string;
  query: string;
  model: string;
  product_source: string;
  time_record: number;
  condense_question: string;
  prompt: string;
  retrieval_documents: string;
  source_documents: string;
  timestamp: string;
  sessionId?: string;
};
const Chat: React.FC = () => {
  const { token } = theme.useToken();
  const params = { id: '23543hkgk' };
  const botInfo = {
    data: {
      icon: 'https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp',
      name: 'AISERSER',
    },
  };
  // ==================== State ====================
  const [conversationsItems, setConversationsItems] = useState<{ key: string; label: string; qa_id: string }[]>([]);
  const [activeKey, setActiveKey] = useState('');
  const [isSidebarOpen, setSiderOpen] = useState(true);
  const [open, setOpen] = useState(false);
  const [chats, setChats] = useState<ChatMessage[]>([]);
  const [newTitle, setNewTitle] = useState<string>('');
  const { start: startChat, stop } = useSse('/ragflow/knowledge_base/knowledge_chat');
  const { userInfo } = useStore();

  // 知识库相关状态
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<number[]>([]);
  const [knowledgeLoading, setKnowledgeLoading] = useState(false);
  const menuConfig: ConversationsProps['menu'] = () => ({
    items: [
      {
        label: '重命名',
        key: 'update',
        icon: <EditOutlined />,
      },
      {
        label: '删除',
        key: 'delete',
        icon: <DeleteOutlined />,
        danger: true,
      },
    ],
    onClick: (menuInfo) => {
      if (menuInfo.key === 'delete') {
        Modal.confirm({
          title: '删除对话',
          icon: <ExclamationCircleFilled />,
          content: '删除后，该对话将不可恢复。确认删除吗？',
          onOk() {
            doGetConverManualDelete({
              params: {
                // sessionId: getExternalSessionId(),
                // bot_id: params?.id,
              },
            }).then((res: any) => {
              if (res.data.errorCode === 0) {
                message.success('删除成功');
                setChats([]);
                // getConversationManualGet({ params: { bot_id: params?.id } });
              }
            });
          },
        });
      } else if (menuInfo.key === 'update') {
        showModal();
      }
    },
  });
  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };

  // 获取知识库列表
  const fetchKnowledgeBases = useCallback(async () => {
    try {
      setKnowledgeLoading(true);
      const response = await getList({
        params: {},
        current: 1,
        pageSize: 100,
      });

      if (response.success) {
        const kbList = response.resp[0]?.list || [];
        setKnowledgeBases(kbList);
      } else {
        message.error(response.msg || '获取知识库列表失败');
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setKnowledgeLoading(false);
    }
  }, []);

  // 处理知识库选择变化
  const handleKnowledgeBasesChange = useCallback((selectedIds: number[]) => {
    setSelectedKnowledgeBases(selectedIds);
    // 保存到本地存储
    localStorage.setItem('selectedKnowledgeBases', JSON.stringify(selectedIds));
  }, []);
  // 更新会话标题的辅助函数
  const updateConversationTitle = (sessionId: string, newTitle: string) => {
    setConversationsItems((prevItems) =>
      prevItems.map((item) => (item.key === sessionId ? { ...item, label: newTitle } : item)),
    );
  };
  const updateTitle = () => {
    doGetConverManualUpdate({
      params: {
        // sessionId: activeKey,
        // bot_id: params?.id,
        // title: newTitle,
      },
    }).then((res: any) => {
      if (res.data.errorCode === 0) {
        // 更新本地状态
        updateConversationTitle(activeKey, newTitle);
        message.success('更新成功');
        setOpen(false);
      }
    });
  };

  const onAddConversation = () => {
    // setNewExternalSessionId();
    // setConversationsItems(prev => [ { key: getExternalSessionId(), label: '新建会话' }, ...prev]);
    setActiveKey(getSessionId());
    // setActiveKey(uuid());
    setChats([]);
  };
  const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = useCallback(
    (key) => {
      setActiveKey(key);
      updateExternalSessionId(key);
      doGetManual({
        user_id: userInfo.id,
        bot_id: key,
        // sessionId: key,
        // bot_id: params?.id,
        // // 是externalBot页面提交的消息记录
        // isExternalMsg: 1,
      }).then((r: any) => {
        setChats(r?.data);
      });
    },
    [userInfo.id],
  );

  const getConversations = (options: IPropsConversationItem[]): { key: string; label: string; qa_id: string }[] => {
    if (options) {
      return options.map((item) => ({
        key: item.bot_id,
        qa_id: item.qa_id,
        label: item.query,
      }));
    }
    return [];
  };
  useEffect(() => {
    if (chats && chats.length === 2 && chats[1].content.length < 1 && userInfo.id) {
      getConversationManualGet({
        user_id: userInfo.id,
        limit: 100,
      }).then((r: any) => {
        setActiveKey(getSessionId());
        setConversationsItems(getConversations(r?.data));
      });
    }
  }, [chats, userInfo.id]);

  // 初始化知识库数据
  useEffect(() => {
    fetchKnowledgeBases();
    // 从本地存储恢复选择的知识库
    const saved = localStorage.getItem('selectedKnowledgeBases');
    if (saved) {
      try {
        const savedIds = JSON.parse(saved);
        if (Array.isArray(savedIds)) {
          setSelectedKnowledgeBases(savedIds);
        }
      } catch (error) {
        console.error('恢复知识库选择失败:', error);
      }
    }
  }, [fetchKnowledgeBases]);

  // 单独的 useEffect 来处理 userInfo 依赖的逻辑
  useEffect(() => {
    // updateExternalSessionId(uuid());
    // 只有当 userInfo.id 存在时才执行
    if (userInfo.id) {
      getConversationManualGet({
        user_id: userInfo.id,
        limit: 100,
      }).then((r: any) => {
        setActiveKey('');
        setConversationsItems(getConversations(r?.data));
      });
    }
  }, [userInfo.id]); // 依赖整个 userInfo 对象

  // ==================== Render =================
  return (
    <Layout curActive="/konwledge/chat" outerStyle={outerStyle}>
      <div className={styles.layout}>
        <div className={`${styles.sider} ${isSidebarOpen ? styles['is-open'] : ''}`}>
          {/* 🌟 Logo */}
          <div className={styles.logo}>
            <span style={{ fontWeight: 'bold', userSelect: 'none' }}>{'对话历史消息'}</span>
            <div className={styles.close} onClick={() => setSiderOpen(!isSidebarOpen)}>
              <MenuOutlined />
            </div>
          </div>
          {/* 🌟 添加会话 */}
          <Button onClick={onAddConversation} type="link" className={styles.addBtn} icon={<PlusOutlined />}>
            新建会话
          </Button>
          {/* 🌟 会话管理 */}
          {conversationsItems && (
            <Conversations
              items={conversationsItems}
              className={styles.conversations}
              activeKey={activeKey}
              menu={menuConfig}
              onActiveChange={onConversationClick}
            />
          )}
        </div>
        {!isSidebarOpen && (
          <div className={styles.isNotOpen} onClick={() => setSiderOpen(!isSidebarOpen)}>
            <MenuOutlined />
          </div>
        )}
        <div className={styles.chat}>
          {/* {chatList}
          {chatSender} */}
          <AiProChat
            style={{ paddingInline: 'calc(calc(100% - 800px) / 2)' }}
            chats={chats}
            onChatsChange={setChats} // 确保正确传递 onChatsChange
            helloMessage="您好，我是赛力斯语析"
            botAvatar={botInfo?.data?.icon}
            request={async (messages) => {
              const readableStream = new ReadableStream({
                async start(controller) {
                  const encoder = new TextEncoder();
                  startChat({
                    data: {
                      bot_id: activeKey || '',
                      user_id: userInfo.id,
                      // sessionId: getExternalSessionId(),
                      question: messages[messages.length - 1].content as string,
                      kb_ids: selectedKnowledgeBases.length > 0 ? selectedKnowledgeBases : [1], // 使用选中的知识库ID
                      streaming: true,
                    },
                    onMessage: (msg) => {
                      controller.enqueue(encoder.encode(msg));
                    },
                    onError: (error) => {
                      controller.error(error);
                    },
                    onFinished: () => {
                      controller.close();
                    },
                  });
                },
              });
              return new Response(readableStream);
            }}
          />
        </div>
      </div>
      {/* senderFooter={
              <KnowledgeSelector
                knowledgeBases={knowledgeBases}
                selectedKnowledgeBases={selectedKnowledgeBases}
                onKnowledgeBasesChange={handleKnowledgeBasesChange}
                loading={knowledgeLoading}
              />
            } */}
      <Modal title="修改会话名称" open={open} onOk={updateTitle} onCancel={hideModal} okText="确认" cancelText="取消">
        <Input
          placeholder="请输入新的会话标题"
          defaultValue={newTitle}
          onChange={(e) => {
            setNewTitle(e.target.value);
          }}
        />
      </Modal>
    </Layout>
  );
};

export default Chat;
