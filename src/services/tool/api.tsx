import req from '@/utils/request';
import { ToolListQuery, ToolPluginUpdate, ToolPluginAdd, ToolAdd, PluginToolListQuery } from '@/types/tool/index';

//查询插件列表
export const aiPluginList = ({ name, pageNumber, pageSize }: ToolListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiPlugin/page?name=${name}&pageNumber=${pageNumber}&pageSize=${pageSize}`);

//新增插件
export const save = (data: ToolPluginAdd): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPlugin/plugin/save`, data);

//编辑插件
export const update = (data: ToolPluginUpdate): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPlugin/plugin/update`, data);

//删除插件
export const remove = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPlugin/plugin/remove`, { id });

//查询工具列表
export const getToolList = ({
  pluginId,
  name,
  pageNumber,
  pageSize,
}: PluginToolListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(
    `/ai-flow/api/v1/aiPluginTool/page?pluginId=${pluginId}&name=${name}&pageNumber=${pageNumber}&pageSize=${pageSize}`,
  );

//新增工具
export const saveTool = (data: ToolAdd): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPluginTool/tool/save`, data);

//删除工具
export const removeTool = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPluginTool/remove`, { id });

//查询工具详情
export const getToolDetail = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPluginTool/tool/search`, { aiPluginToolId: id });

//更新工具
export const updateTool = (data: any): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiPluginTool/tool/update`, data);
