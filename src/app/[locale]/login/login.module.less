.main {
    display: flex; // 添加 flex 布局
    flex-direction: column;
    align-items: center; // 垂直居中
    justify-content: space-between; // 水平居中
    width: 100vw;
    height: 100vh;
    padding: 30px;
    padding-bottom: 10px;
    background-image: url("/images/logo_bg.jpeg");
    background-size: cover
}

.container {
    width: 400px;
    height: 500px;
    margin-top: 6%;
    padding: 30px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
}

.copyright {
    color: #999;
    font-size: 14px;
}

.title {
    display: flex;
    gap: 10px;
    justify-content: center;
    width: 100%;
    margin-bottom: 15px;
    padding: 10px 20px;
    font-size: 26px;
    color: #1677ff;

    .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        color: #fff;
        font-size: 26px;
        line-height: 40px;
        background: #1677ff;
        border-radius: 50%;
    }

}