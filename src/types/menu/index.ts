export interface CreateMenuData {
  parent?: number;
  parentId?: any;
  parentCode?: string;
  parentPath?: string;
  name: string;
  code: string;
  path: string;
  icon: string;
  orderNo: number;
  remark: string;
}

export interface UpdateMenuData extends CreateMenuData {
  key: number;
}

export interface ListDataType {
  key: number;
  title: string;
  code: string;
  path: string;
  icon?: string;
  orderNo?: number;
  remark?: string;
  parentId?: number;
  parentCode?: string;
  parentPath?: string;
  children?: ListDataType[];
  functionChildren?: FunctionAuthoritys[];
}

export interface FunctionAuthoritys {
  key?: number | null;
  title: string;
  code: string;
  orderNo?: number;
  remark?: string;
}

export interface UpdateFunctionData {
  functionAuthoritys?: FunctionAuthoritys[];
  parent?: number;
  level?: number;
}
