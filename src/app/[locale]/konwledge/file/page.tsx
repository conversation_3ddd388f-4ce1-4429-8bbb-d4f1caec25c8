'use client';
import { useState, useEffect, useCallback } from 'react';
import { Card, Input, Button, Table, Space, message, Typography, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import AddFileModal from './components/addFileModal';
import RelationModal from './components/relationModal';
import { download, getDocList } from '@/services/konwledge/api';
import zhCN from 'antd/es/locale/zh_CN';
import debounce from 'lodash-es/debounce';
import dayjs from 'dayjs';

const { Title } = Typography;
const { confirm } = Modal;

interface ListTemplate {
  id: number;
  name: string;
  createTime: string;
  size: string;
  datasets: string;
  documentPath: string;
}

const KonwledgeFilePage: React.FC = () => {
  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ListTemplate[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [relationModalOpen, setRelaitonModalOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Partial<ListTemplate>>();
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    handleSearch();
  }, []);

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },

    {
      title: '上传日期',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (_, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm') : '-'),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 120,
    },
    {
      title: '知识库',
      dataIndex: 'datasets',
      key: 'datasets',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          {/* <a onClick={() => relationHandle(record)}>关联知识库</a> */}
          <a onClick={() => downloadHandle(record)}>下载</a>
          <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
            删除
          </a>
        </Space>
      ),
    },
  ];

  const handleSearch = async (name?: string) => {
    try {
      setLoading(true);
      const response = await getDocList({
        params: { name },
        current,
        pageSize,
      });
      if (response.success) {
        setData(response.resp[0].list);
        setTotal(response.resp[0]?.pagination?.total);
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setModalOpen(true);
  };

  const relationHandle = (record: ListTemplate) => {
    setRelaitonModalOpen(true);
  };

  const downloadHandle = async (record: ListTemplate) => {
    // const fileId = record.id; // 假设每个文件有唯一 ID
    // if (!fileId) {
    //   message.error("文件信息不完整，无法下载");
    //   return;
    // }
    // // 显示加载状态
    // message.loading({ content: "正在准备下载...", key: "download" });
    // 调用后端接口下载文件
    // const respData = await download(fileId);
    // .then((response) => {
    //   if (!response.success) {
    //     throw new Error("下载失败，请稍后再试");
    //   }
    //   return response.blob(); // 获取文件流
    // })
    // .then((blob) => {
    //   // 创建下载链接
    //   const url = window.URL.createObjectURL(blob);
    //   const a = document.createElement("a");
    //   a.href = url;
    //   a.download = record.name; // 设置文件名
    //   document.body.appendChild(a);
    //   a.click();
    //   window.URL.revokeObjectURL(url); // 释放对象 URL
    //   document.body.removeChild(a);
    //   // 提示下载成功
    //   message.success({ content: "下载成功", key: "download" });
    // })
    // .catch((error) => {
    //   message.error({ content: "下载失败，请稍后再试", key: "download" });
    // });
    window.open(record.documentPath, '_blank');
  };

  const handleDelete = (record: ListTemplate) => {
    confirm({
      title: '确认删除',
      content: `是否删除该文件：${record.name}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        try {
          // TODO: 实现删除逻辑
          message.success('删除成功');
          handleSearch(searchName); // 刷新列表
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleModalSuccess = () => {
    setModalOpen(false);
    handleSearch(searchName);
  };

  const handleRelationModalSuccess = () => {
    setRelaitonModalOpen(false);
    handleSearch(searchName);
  };

  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  const changeHandle = useCallback(
    debounce((value: string) => {
      setSearchName(value);
      handleSearch(value);
    }, 500),
    [],
  );

  return (
    <Layout curActive="/konwledge/file">
      <div>
        {/* <Title level={2} style={{ marginBottom: 24 }}>
          文件管理
        </Title> */}
        <Card variant="borderless">
          <div
            style={{
              marginBottom: 24,
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <Space>
              {/* <Select
                defaultValue="0"
                size="middle"
                style={{ width: 130 }}
                onChange={selectChange}
                options={[{ value: "0", label: "批量" }]}
              /> */}
              <Input
                size="middle"
                placeholder="搜索文件"
                onChange={(e) => changeHandle(e.target.value)}
                prefix={<SearchOutlined />}
                allowClear
              />
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增文件
              </Button>
            </Space>
          </div>
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1300 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              defaultPageSize: 10,
              locale: zhCN.Pagination, // 设置分页的本地化为中文
              pageSize,
              onChange: PageChange,
              current,
              onShowSizeChange: PageChange,
              total,
            }}
          />
        </Card>

        <AddFileModal open={modalOpen} onCancel={() => setModalOpen(false)} onSuccess={handleModalSuccess} />

        <RelationModal
          open={relationModalOpen}
          onCancel={() => setRelaitonModalOpen(false)}
          onSuccess={handleRelationModalSuccess}
        />
      </div>
    </Layout>
  );
};

export default KonwledgeFilePage;
