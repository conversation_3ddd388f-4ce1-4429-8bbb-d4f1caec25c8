// 设置缓存/获取缓存
interface PaginationParams {
  current: number;
  pageSize: number;
}

interface StorageType {
  params?: any;
  pagination?: PaginationParams;
  hash: string;
}

interface StorageMap {
  [key: string]: {
    params?: any; // 可选字段
    pagination?: PaginationParams; // 可选字段
  };
}

const RawData: StorageMap = {};
const setStorage = (data: StorageType) => {
  const { params, pagination, hash } = data;
  RawData[hash] = {
    ...RawData[hash], // 保留已有数据
    params,
    pagination,
  };
};
const getStorage = (hash: string) => RawData[hash];
const removeStorage = (hash: string) => {
  if (RawData[hash]) {
    delete RawData[hash];
  }
};

const userId = () => {
  const storageData = localStorage.getItem('seres-storage');
  const state = storageData ? JSON.parse(storageData) : null;
  return state?.userInfo?.id || '';
};

export { setStorage, getStorage, removeStorage, userId };
