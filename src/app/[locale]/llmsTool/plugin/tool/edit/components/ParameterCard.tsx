'use client';
import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, Select, Switch, Table, Space, message, Tooltip } from 'antd';
import { EditOutlined, SaveOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { updateTool } from '@/services/tool/api';

const { Option } = Select;

interface ParameterItem {
  name: string;
  description: string;
  type: string;
  required: boolean;
  enabled: boolean;
  key?: string;
  method?: string;
  defaultValue?: string;
}

interface ParameterCardProps {
  title: string;
  toolId: string;
  parameters: ParameterItem[];
  fieldName: 'inputData' | 'outputData';
  onUpdate: () => void;
  type: 'input' | 'output';
}

const ParameterCard: React.FC<ParameterCardProps> = ({ title, toolId, parameters, fieldName, onUpdate, type }) => {
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();

  // 设置表单初始值
  useEffect(() => {
    if (parameters) {
      form.setFieldsValue({
        [fieldName]: parameters,
      });
    }
  }, [parameters, form, fieldName]);

  // 当点击编辑时，重新设置表单值
  const handleEdit = () => {
    // 先设置表单值，再切换编辑状态
    form.setFieldsValue({
      [fieldName]: parameters,
    });
    setEditing(true);
  };

  // 保存参数
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const updateData = {
        id: toolId,
        [fieldName]: JSON.stringify(values[fieldName] || []),
      };

      const { errorCode } = await updateTool(updateData);

      if (errorCode === 0) {
        message.success('保存成功');
        setEditing(false);
        onUpdate();
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleCancel = () => {
    setEditing(false);
    // 重置表单到原始值
    form.setFieldsValue({
      [fieldName]: parameters,
    });
  };

  // 参数表格列定义
  const getParameterColumns = () => [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string, _: any, index: number) => {
        if (editing) {
          return (
            <Form.Item
              name={[index, 'name']}
              rules={[{ required: true, message: '请输入参数名称' }]}
              style={{ margin: 0 }}
            >
              <Input placeholder="参数名称" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '参数描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string, ___: any, index: number) => {
        if (editing) {
          return (
            <Form.Item
              rules={[{ required: true, message: '请输入参数名称' }]}
              name={[index, 'description']}
              style={{ margin: 0 }}
            >
              <Input placeholder="参数描述" />
            </Form.Item>
          );
        }
        return (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '参数类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (text: string, ____: any, index: number) => {
        if (editing) {
          return (
            <Form.Item
              name={[index, 'type']}
              rules={[{ required: true, message: '请选择参数类型' }]}
              style={{ margin: 0 }}
            >
              <Select placeholder="选择类型">
                <Option value="String">String</Option>
                <Option value="Number">Number</Option>
                <Option value="Boolean">Boolean</Option>
                <Option value="Array">Array</Option>
                <Option value="Object">Object</Option>
              </Select>
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '传入方法',
      dataIndex: 'method',
      key: 'method',
      width: 120,
      hidden: type === 'output',
      render: (text: string, ____: any, index: number) => {
        if (editing) {
          return (
            <Form.Item
              name={[index, 'method']}
              rules={[{ required: true, message: '请选择传入方法' }]}
              style={{ margin: 0 }}
            >
              <Select placeholder="选择方法">
                <Option value="Query">Query</Option>
                <Option value="Body">Body</Option>
                <Option value="Patch">Patch</Option>
                <Option value="Header">Header</Option>
              </Select>
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
      width: 100,
      hidden: type === 'output',
      render: (value: boolean, _____: any, index: number) => {
        if (editing) {
          return (
            <Form.Item name={[index, 'required']} valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          );
        }
        return value ? '是' : '否';
      },
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      hidden: type === 'output',
      width: 200,
      render: (text: string, ___: any, index: number) => {
        if (editing) {
          return (
            <Form.Item name={[index, 'defaultValue']} style={{ margin: 0 }}>
              <Input placeholder="默认值" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '启用状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 100,
      render: (value: boolean, ______: any, index: number) => {
        if (editing) {
          return (
            <Form.Item name={[index, 'enabled']} valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          );
        }
        return value ? '启用' : '禁用';
      },
    },
  ];

  return (
    <Card
      title={title}
      variant="borderless"
      extra={
        <Space>
          {editing ? (
            <>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
                保存
              </Button>
            </>
          ) : (
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              编辑
            </Button>
          )}
        </Space>
      }
      style={{ marginBottom: '24px' }}
      styles={{
        header: {
          background: '#d6ebff',
          borderRadius: 0,
        },
        body: {
          background: '#f1f8ff',
          borderRadius: 0,
        },
      }}
    >
      {editing ? (
        <Form form={form}>
          <Form.List name={fieldName}>
            {(fields, { add, remove }) => (
              <>
                <Table
                  bordered
                  dataSource={fields.map((field, index) => ({
                    key: field.key,
                    index,
                    fieldKey: field.key,
                    fieldName: field.name,
                  }))}
                  columns={[
                    ...getParameterColumns(),
                    {
                      title: '操作',
                      key: 'action',
                      width: 80,
                      render: (_: any, __: any, index: number) => (
                        <Button type="link" danger icon={<DeleteOutlined />} onClick={() => remove(index)} />
                      ),
                    },
                  ]}
                  pagination={false}
                  size="small"
                />
                <Button
                  type="dashed"
                  onClick={() =>
                    add({
                      name: '',
                      description: '',
                      type: 'String',
                      required: false,
                      enabled: true,
                      method: 'Query',
                      defaultValue: '',
                    })
                  }
                  icon={<PlusOutlined />}
                  style={{ marginTop: '16px' }}
                >
                  添加参数
                </Button>
              </>
            )}
          </Form.List>
        </Form>
      ) : (
        <Table
          bordered
          dataSource={parameters.map((param, index) => ({
            key: index,
            index,
            ...param,
          }))}
          columns={getParameterColumns()}
          pagination={false}
          size="small"
        />
      )}
    </Card>
  );
};

export default ParameterCard;
