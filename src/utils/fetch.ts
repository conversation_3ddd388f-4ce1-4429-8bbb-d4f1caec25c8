import Cookies from 'js-cookie';

// 流式响应处理器类型
export interface StreamHandler {
  onMessage?: (data: any) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

// 流式请求配置
export interface StreamRequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  handler: StreamHandler;
}

/**
 * 发送流式请求
 * @param config 请求配置
 */
export async function fetchStream(config: StreamRequestConfig): Promise<void> {
  const {
    url,
    method = 'POST',
    headers = {},
    body,
    handler
  } = config;

  try {
    // 构建完整的URL
    const fullUrl = url.startsWith('http') 
      ? url 
      : `${process.env.NEXT_PUBLIC_AI_BASE_URL}${url}`;

    // 默认请求头
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Authorization': Cookies.get('access_token') || '',
      ...headers
    };

    console.log('发送流式请求:', {
      url: fullUrl,
      method,
      headers: defaultHeaders,
      body
    });

    // 发送请求
    const response = await fetch(fullUrl, {
      method,
      headers: defaultHeaders,
      body: body ? JSON.stringify(body) : undefined,
    });

    console.log('收到响应:', {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('请求失败:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    // 处理流式响应
    await handleStreamResponse(response, handler);

  } catch (error) {
    console.error('流式请求错误:', error);
    handler.onError?.(error as Error);
  }
}

/**
 * 处理流式响应
 * @param response 响应对象
 * @param handler 处理器
 */
async function handleStreamResponse(response: Response, handler: StreamHandler): Promise<void> {
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('无法获取响应流');
  }

  const decoder = new TextDecoder();
  let buffer = '';

  try {
    console.log('开始读取流数据...');
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('流数据读取完成');
        handler.onComplete?.();
        break;
      }

      // 解码数据块
      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      // 按行分割数据
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      // 处理每一行
      for (const line of lines) {
        const trimmedLine = line.trim();
        
        if (!trimmedLine) {
          continue;
        }

        try {
          // 尝试解析数据
          let dataMessage = '';
          
          // 检查是否是SSE格式
          if (trimmedLine.startsWith('data: ')) {
            dataMessage = trimmedLine.replace('data: ', '').trim();
          } else {
            // 直接作为JSON数据处理
            dataMessage = trimmedLine;
          }

          if (!dataMessage || dataMessage === '[DONE]') {
            continue;
          }

          // 解析JSON数据
          const parsed = JSON.parse(dataMessage);
          console.log('解析到数据:', parsed);
          
          // 调用消息处理器
          handler.onMessage?.(parsed);

        } catch (parseError) {
          console.warn('数据解析失败:', parseError, '原始数据:', trimmedLine);
          
          // 尝试处理多个JSON对象连接的情况
          if (trimmedLine.includes('}{')) {
            const jsonObjects = trimmedLine.split('}{');
            for (let i = 0; i < jsonObjects.length; i++) {
              let jsonStr = jsonObjects[i];
              if (i > 0) jsonStr = '{' + jsonStr;
              if (i < jsonObjects.length - 1) jsonStr = jsonStr + '}';
              
              try {
                const parsed = JSON.parse(jsonStr);
                console.log(`分割解析成功 ${i + 1}:`, parsed);
                handler.onMessage?.(parsed);
              } catch (splitError) {
                console.warn(`分割解析失败 ${i + 1}:`, splitError);
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('流处理错误:', error);
    handler.onError?.(error as Error);
  } finally {
    reader.releaseLock();
  }
}

/**
 * 工作流流式执行请求
 * @param data 请求数据
 * @param handler 处理器
 */
export async function tryRunningStream(
  data: { id: string; variables?: Record<string, any> },
  handler: StreamHandler
): Promise<void> {
  return fetchStream({
    url: '/ai-flow/api/v1/aiWorkflow/tryRunningStream',
    method: 'POST',
    body: data,
    handler
  });
}
