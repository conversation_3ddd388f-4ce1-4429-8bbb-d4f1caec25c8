import { events } from 'fetch-event-stream';
import { useState } from 'react';
import Cookies from 'js-cookie';
type StartParams = {
  data: any;
  onMessage: (message: string) => void;
  onError?: (err?: Error) => void;
  onFinished: () => void;
};

// const baseUrl = `${import.meta.env.VITE_APP_SERVER_ENDPOINT}/`;
// const authKey = `${import.meta.env.VITE_APP_AUTH_KEY || 'authKey'}`;
// const tokenKey = `${import.meta.env.VITE_APP_TOKEN_KEY}`;
const baseUrl = `${process.env.NEXT_PUBLIC_AI_BASE_URL}/`;
export const useSse = (url: string, headers?: any, options?: any) => {
  const ctrl = new AbortController();
  const [loading, setLoading] = useState(false);

  let sseUrl = url;
  if (sseUrl.startsWith('/')) {
    sseUrl = baseUrl + sseUrl.substring(1);
  }

  const token = typeof window !== 'undefined' ? Cookies.get('access_token') : null;
  const sseHeaders = {
    Authorization: token || '',
    'Content-Type': 'application/json',
    ...headers,
  };

  return {
    loading: loading,
    stop: () => {
      ctrl.abort('by stop() invoked!');
      setLoading(false);
    },
    start: async (params: StartParams) => {
      try {
        setLoading(true);
        const res = await fetch(sseUrl, {
          method: 'post',
          signal: ctrl.signal,
          headers: sseHeaders,
          body: JSON.stringify(params.data),
        });
        debugger;
        if (!res.ok) {
          params.onError?.();
          return;
        }
        try {
          const msgEvents = events(res, ctrl.signal);
          for await (const event of msgEvents) {
            if (event.data && '[DONE]' !== event.data.trim()) {
              if (options === 'ollamaInstall') {
                params.onMessage(event.data);
              } else {
                const temp = JSON.parse(event.data);
                params.onMessage(temp?.choices?.[0]?.delta?.content);
              }
            }
          }
        } catch (err) {
          console.error('error', err);
          params.onError?.();
        } finally {
          params.onFinished();
        }
      } finally {
        setLoading(false);
      }
    },
  };
};
